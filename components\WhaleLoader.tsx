import React, { useRef, useState, useEffect, useMemo } from 'react';
import { 
  StyleSheet, 
  View, 
  ActivityIndicator, 
  StyleProp, 
  ViewStyle,
  Platform
  // Text // Removed unused Text import
} from 'react-native';
import LottieView from 'lottie-react-native';

// Import animation assets
const whaleAnimationData = require('@assets/hodlhub-whale-loading/hodlhub-whale-loading.json');
const whaleImage = require('@assets/hodlhub-whale-loading/images/img_0.png');

interface WhaleLoaderProps {
  size?: number;
  style?: StyleProp<ViewStyle>;
  speed?: number;
  color?: string;
  showFallback?: boolean;
}

/**
 * Universal Whale Loading Component
 * 
 * Features:
 * - Lottie animation with fallback support
 * - Cross-platform compatibility (iOS/Android/Web)
 * - Automatic error handling and recovery
 * - Optimized asset loading
 * - Consistent 200x200px base sizing
 * - Counter-clockwise rotation animation
 */
const WhaleLoader: React.FC<WhaleLoaderProps> = ({
  size = 200,
  style,
  speed = 1,
  color = '#3EC1F9',
  showFallback = false,
}) => {
  const [isReady, setIsReady] = useState(false);
  const [hasError, setHasError] = useState(false);
  const animationRef = useRef<LottieView>(null);

  // Prepare optimized animation source
  const animationSource = useMemo(() => {
    try {
      // Create a working copy of the animation data
      const source = JSON.parse(JSON.stringify(whaleAnimationData));
      
      // Ensure proper dimensions
      source.w = source.w || 200;
      source.h = source.h || 200;
      
      // Update asset paths for proper bundling.
      // Lottie animations can reference external image assets.
      // The Lottie JSON contains an 'assets' array where these images are declared.
      // Each asset object has a 'p' property for the filename (e.g., 'img_0.png')
      // and a 'u' property for the path (e.g., 'images/').
      // To make this work seamlessly with React Native's asset system (which bundles
      // images referenced by `require()`), we modify the Lottie JSON in memory:
      // 1. We find the image asset entry (by 'id' or 'p' property).
      // 2. We replace its 'p' property with the result of `require(pathToImage)`.
      //    On native, this `require()` returns a resource ID. On web, it might be a path or data URI.
      // 3. We clear the 'u' property (path) as the asset is now directly referenced.
      // 4. We set 'e: 0` (e.g., e=1 for external, e=0 for embedded). Lottie uses this to know
      //    whether it needs to fetch the asset or if it's provided in the 'p' field.
      // This allows Lottie to load the image correctly on all platforms without needing
      // a separate 'assetPath' prop, which is not universally supported or straightforward
      // for all platforms (especially web) in `lottie-react-native`.
      if (source.assets && Array.isArray(source.assets)) {
        source.assets = source.assets.map((asset: any) => {
          // Identify the specific whale image asset within the Lottie JSON.
          // It might be identified by 'id' or its original filename 'img_0.png'.
          if (asset.id === 'image_0' || asset.p === 'img_0.png') {
            return {
              ...asset,
              p: whaleImage, // Replace filename with the required image asset (resource ID or path/data URI)
              u: '',         // Clear the original subfolder path ('images/') as 'p' now holds the full reference.
              e: 0           // Mark as embedded/not-external so Lottie uses the 'p' field directly.
            };
          }
          return asset;
        });
      }
      
      return source;
    } catch (error) {
      console.error('WhaleLoader: Failed to prepare animation source:', error);
      setHasError(true);
      return whaleAnimationData;
    }
  }, []);

  // Handle animation lifecycle
  const handleAnimationLoaded = () => {
    setIsReady(true);
    setHasError(false);
  };

  const handleAnimationFailure = (error: any) => {
    console.error('WhaleLoader: Animation failed:', error);
    setHasError(true);
  };

  // Reset error state when props change
  useEffect(() => {
    if (hasError) {
      setHasError(false);
      setIsReady(false);
    }
  }, [size, speed]);

  // Render fallback for errors or web compatibility
  if (hasError || showFallback || (Platform.OS === 'web' && !isReady)) {
    return (
      <View style={[styles.container, { width: size, height: size }, style]}>
        <ActivityIndicator 
          size={size > 100 ? 'large' : 'small'} 
          color={color} 
        />
      </View>
    );
  }

  return (
    <View style={[styles.container, { width: size, height: size }, style]}>
      <LottieView
        ref={animationRef}
        source={animationSource}
        autoPlay
        loop
        speed={speed}
        style={{
          width: size,
          height: size,
        }}
        resizeMode="contain"
        onAnimationLoaded={handleAnimationLoaded}
        onAnimationFailure={handleAnimationFailure}
        // Web-specific optimizations
        {...(Platform.OS === 'web' && {
          renderer: 'svg',
          rendererSettings: {
            preserveAspectRatio: 'xMidYMid meet',
            clearCanvas: false,
            progressiveLoad: true,
          }
        })}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
});

export default React.memo(WhaleLoader);
