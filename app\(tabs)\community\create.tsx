import React, { useState, useCallback, useMemo } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TextInput, 
  TouchableOpacity, 
  ScrollView, 
  Image, 
  Switch, 
  Platform, 
  Alert, 
  Animated,
  Dimensions,
  KeyboardAvoidingView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import LoadingWhale from '../../../components/ui/LoadingWhale';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

type Privacy = 'public' | 'private';
type Category = 'trading' | 'defi' | 'nft' | 'tech' | 'general';
type PaymentMethod = 'paypal';

const categories = [
  { id: 'trading', label: 'Trading', icon: '📈', color: '#00D4AA', description: 'Market analysis & strategies' },
  { id: 'defi', label: 'DeFi', icon: '🏦', color: '#FF6B6B', description: 'Decentralized finance' },
  { id: 'nft', label: 'NFTs & Digital Art', icon: '🎨', color: '#4ECDC4', description: 'Digital collectibles' },
  { id: 'tech', label: 'Technology', icon: '💻', color: '#45B7D1', description: 'Tech discussions' },
  { id: 'general', label: 'General Discussion', icon: '💬', color: '#96CEB4', description: 'Open conversations' },
] as const;

interface ValidationErrors {
  name?: string;
  description?: string;
  rules?: string;
  price?: string;
  paymentMethod?: string;
}

interface PaymentMethodSelectorProps {
  onSelect: (method: PaymentMethod) => void;
  price: number;
  selectedMethod: PaymentMethod | null;
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({ onSelect, price, selectedMethod }) => {
  const methods = [
    {
      id: 'paypal' as PaymentMethod,
      name: 'PayPal',
      icon: 'logo-paypal',
      description: 'Secure payments via PayPal',
      color: '#0070BA'
    }
  ];

  return (
    <View style={styles.paymentMethodsContainer}>
      <Text style={styles.sectionLabel}>Payment Method</Text>
      <Text style={styles.sectionDescription}>Choose how members will pay</Text>
      
      {methods.map((method) => (
        <TouchableOpacity
          key={method.id}
          style={[
            styles.paymentMethod, 
            selectedMethod === method.id && styles.selectedPaymentMethod
          ]}
          onPress={() => onSelect(method.id)}
          activeOpacity={0.7}
        >
          <View style={styles.paymentMethodLeft}>
            <View style={[styles.paymentIconContainer, { backgroundColor: method.color + '20' }]}>
              <Ionicons 
                name={method.icon as any} 
                size={24} 
                color={selectedMethod === method.id ? method.color : '#666'} 
              />
            </View>
            <View style={styles.paymentMethodInfo}>
              <Text style={[
                styles.paymentMethodText, 
                selectedMethod === method.id && styles.selectedPaymentMethodText
              ]}>
                {method.name}
              </Text>
              <Text style={styles.paymentMethodDescription}>
                {method.description}
              </Text>
            </View>
          </View>
          <View style={styles.paymentMethodRight}>
            <Text style={styles.paymentMethodPrice}>
              ${price.toFixed(2)}/mo
            </Text>
            {selectedMethod === method.id && (
              <Ionicons name="checkmark-circle" size={20} color={method.color} />
            )}
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const FormProgress: React.FC<{ step: number; totalSteps: number }> = ({ step, totalSteps }) => {
  const progress = (step / totalSteps) * 100;
  
  return (
    <View style={styles.progressContainer}>
      <View style={styles.progressBar}>
        <Animated.View 
          style={[
            styles.progressFill, 
            { width: `${progress}%` }
          ]} 
        />
      </View>
      <Text style={styles.progressText}>{step}/{totalSteps} completed</Text>
    </View>
  );
};

const InputField: React.FC<{
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder: string;
  maxLength?: number;
  multiline?: boolean;
  numberOfLines?: number;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  description?: string;
}> = ({ 
  label, 
  value, 
  onChangeText, 
  placeholder, 
  maxLength, 
  multiline, 
  numberOfLines, 
  error, 
  required, 
  disabled,
  description 
}) => {
  const [isFocused, setIsFocused] = useState(false);
  
  return (
    <View style={styles.inputContainer}>
      <View style={styles.labelContainer}>
        <Text style={styles.label}>
          {label} {required && <Text style={styles.required}>*</Text>}
        </Text>
        {description && <Text style={styles.inputDescription}>{description}</Text>}
      </View>
      
      <View style={[
        styles.inputWrapper,
        isFocused && styles.inputWrapperFocused,
        error && styles.inputWrapperError
      ]}>
        <TextInput
          style={[styles.input, multiline && styles.textArea]}
          placeholder={placeholder}
          placeholderTextColor="#666"
          value={value}
          onChangeText={onChangeText}
          maxLength={maxLength}
          multiline={multiline}
          numberOfLines={numberOfLines}
          textAlignVertical={multiline ? "top" : "center"}
          editable={!disabled}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        />
      </View>
      
      <View style={styles.inputFooter}>
        {error && <Text style={styles.errorText}>{error}</Text>}
        {maxLength && (
          <Text style={[styles.charCount, error && styles.charCountError]}>
            {value.length}/{maxLength}
          </Text>
        )}
      </View>
    </View>
  );
};

export default function CreateCommunityScreen() {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [rules, setRules] = useState('');
  const [privacy, setPrivacy] = useState<Privacy>('public');
  const [selectedCategory, setSelectedCategory] = useState<Category>('general');
  const [imageUrl, setImageUrl] = useState('');
  const [isPaid, setIsPaid] = useState(false);
  const [price, setPrice] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [currentStep, setCurrentStep] = useState(1);

  // Real-time validation
  const validateField = useCallback((field: keyof ValidationErrors, value: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      
      switch (field) {
        case 'name':
          if (!value.trim()) {
            newErrors.name = 'Community name is required';
          } else if (value.length < 3) {
            newErrors.name = 'Name must be at least 3 characters';
          } else if (value.length > 50) {
            newErrors.name = 'Name cannot exceed 50 characters';
          } else {
            delete newErrors.name;
          }
          break;
          
        case 'description':
          if (!value.trim()) {
            newErrors.description = 'Description is required';
          } else if (value.length < 10) {
            newErrors.description = 'Description must be at least 10 characters';
          } else {
            delete newErrors.description;
          }
          break;
          
        case 'rules':
          if (!value.trim()) {
            newErrors.rules = 'Community rules are required';
          } else if (value.length < 20) {
            newErrors.rules = 'Rules must be at least 20 characters';
          } else {
            delete newErrors.rules;
          }
          break;
          
        case 'price':
          if (isPaid) {
            const numPrice = parseFloat(value);
            if (!value || isNaN(numPrice)) {
              newErrors.price = 'Price is required for premium communities';
            } else if (numPrice <= 0) {
              newErrors.price = 'Price must be greater than 0';
            } else if (numPrice > 999.99) {
              newErrors.price = 'Price cannot exceed $999.99';
            } else {
              delete newErrors.price;
            }
          } else {
            delete newErrors.price;
          }
          break;
      }
      
      return newErrors;
    });
  }, [isPaid]);

  // Handle field changes with validation
  const handleNameChange = useCallback((text: string) => {
    setName(text);
    validateField('name', text);
    if (text.length >= 3) setCurrentStep(Math.max(currentStep, 2));
  }, [validateField, currentStep]);

  const handleDescriptionChange = useCallback((text: string) => {
    setDescription(text);
    validateField('description', text);
    if (text.length >= 10) setCurrentStep(Math.max(currentStep, 3));
  }, [validateField, currentStep]);

  const handleRulesChange = useCallback((text: string) => {
    setRules(text);
    validateField('rules', text);
    if (text.length >= 20) setCurrentStep(Math.max(currentStep, 4));
  }, [validateField, currentStep]);

  const handlePriceChange = useCallback((text: string) => {
    const numericValue = text.replace(/[^0-9.]/g, '');
    const decimalCount = (numericValue.match(/\./g) || []).length;
    if (decimalCount <= 1) {
      setPrice(numericValue);
      validateField('price', numericValue);
    }
  }, [validateField]);

  const handleCreate = async () => {
    if (isSubmitting) return;
    
    // Final validation
    validateField('name', name);
    validateField('description', description);
    validateField('rules', rules);
    if (isPaid) {
      validateField('price', price);
      if (!selectedPaymentMethod) {
        setErrors(prev => ({ ...prev, paymentMethod: 'Please select a payment method' }));
        return;
      }
    }
    
    if (Object.keys(errors).length > 0) {
      Alert.alert('Validation Error', 'Please fix the errors before creating the community');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 2500));
      
      const communityData = {
        name: name.trim(),
        description: description.trim(),
        rules: rules.trim(),
        privacy,
        category: selectedCategory,
        imageUrl: imageUrl.trim(),
        isPaid,
        price: isPaid ? parseFloat(price) : 0,
        paymentMethod: selectedPaymentMethod,
      };
      
      console.log('Creating community with data:', communityData);
      
      Alert.alert(
        'Success! 🎉', 
        `Your community "${name}" has been created successfully! Members can now discover and join your community.`,
        [{ 
          text: 'View Community', 
          onPress: () => router.back() 
        }]
      );
    } catch (error) {
      console.error('Error creating community:', error);
      Alert.alert(
        'Creation Failed', 
        'We couldn\'t create your community right now. Please check your connection and try again.',
        [{ text: 'Try Again', style: 'default' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleImageUpload = () => {
    Alert.alert(
      'Add Community Image', 
      'Choose how you\'d like to add your community image',
      [
        { text: 'Camera', onPress: () => console.log('Camera selected') },
        { text: 'Photo Library', onPress: () => console.log('Library selected') },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const isFormValid = useMemo(() => {
    return name.trim().length >= 3 && 
           description.trim().length >= 10 && 
           rules.trim().length >= 20 && 
           selectedCategory && 
           Object.keys(errors).length === 0 &&
           (!isPaid || (isPaid && parseFloat(price) > 0 && selectedPaymentMethod));
  }, [name, description, rules, selectedCategory, errors, isPaid, price, selectedPaymentMethod]);

  const priceValue = parseFloat(price) || 0;
  const totalSteps = 5;

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Enhanced Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.closeButton}
          onPress={() => router.back()}
          disabled={isSubmitting}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <Text style={styles.title}>Create Community</Text>
          <FormProgress step={currentStep} totalSteps={totalSteps} />
        </View>
        
        <TouchableOpacity 
          style={[styles.createButton, (!isFormValid || isSubmitting) && styles.createButtonDisabled]}
          onPress={handleCreate}
          disabled={!isFormValid || isSubmitting}
        >
          {isSubmitting ? (
            <View style={styles.loadingContainer}>
              <LoadingWhale size={16} color="#fff" />
            </View>
          ) : (
            <Text style={styles.createButtonText}>Create</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Enhanced Image Upload */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Community Image</Text>
          <Text style={styles.sectionDescription}>Add a visual identity to your community</Text>
          
          <TouchableOpacity style={styles.imageUpload} onPress={handleImageUpload}>
            {imageUrl ? (
              <View style={styles.imageContainer}>
                <Image 
                  source={{ uri: imageUrl }} 
                  style={styles.communityImage} 
                  resizeMode="cover"
                />
                <TouchableOpacity style={styles.imageEditButton} onPress={handleImageUpload}>
                  <Ionicons name="pencil" size={16} color="#fff" />
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.imageUploadContent}>
                <LinearGradient
                  colors={['#3EC1F9', '#4ECDC4']}
                  style={styles.imagePlaceholder}
                >
                  <Ionicons name="camera-outline" size={32} color="#fff" />
                </LinearGradient>
                <Text style={styles.imageUploadText}>Add Community Image</Text>
                <Text style={styles.imageUploadSubtext}>Upload a logo or banner</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* Enhanced Form Fields */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>
          
          <InputField
            label="Community Name"
            value={name}
            onChangeText={handleNameChange}
            placeholder="Enter community name"
            maxLength={50}
            error={errors.name}
            required
            disabled={isSubmitting}
            description="Choose a unique and memorable name"
          />

          <InputField
            label="Description"
            value={description}
            onChangeText={handleDescriptionChange}
            placeholder="What's your community about?"
            maxLength={500}
            multiline
            numberOfLines={4}
            error={errors.description}
            required
            disabled={isSubmitting}
            description="Help people understand your community's purpose"
          />

          <InputField
            label="Community Rules"
            value={rules}
            onChangeText={handleRulesChange}
            placeholder="Set some ground rules for your community"
            maxLength={1000}
            multiline
            numberOfLines={4}
            error={errors.rules}
            required
            disabled={isSubmitting}
            description="Clear guidelines help maintain a positive environment"
          />
        </View>

        {/* Enhanced Category Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Category</Text>
          <Text style={styles.sectionDescription}>Help people discover your community</Text>
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesContainer}
            contentContainerStyle={styles.categoriesContent}
          >
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryCard,
                  selectedCategory === category.id && styles.selectedCategoryCard
                ]}
                onPress={() => {
                  setSelectedCategory(category.id as Category);
                  setCurrentStep(Math.max(currentStep, 4));
                }}
                disabled={isSubmitting}
              >
                <View style={[
                  styles.categoryIconContainer,
                  { backgroundColor: category.color + '20' }
                ]}>
                  <Text style={styles.categoryIcon}>{category.icon}</Text>
                </View>
                <Text style={[
                  styles.categoryTitle,
                  selectedCategory === category.id && { color: category.color }
                ]}>
                  {category.label}
                </Text>
                <Text style={styles.categoryDescription}>
                  {category.description}
                </Text>
                {selectedCategory === category.id && (
                  <View style={[styles.selectedIndicator, { backgroundColor: category.color }]}>
                    <Ionicons name="checkmark" size={14} color="#fff" />
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Enhanced Privacy Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy Settings</Text>
          <Text style={styles.sectionDescription}>Control who can join your community</Text>
          
          <View style={styles.privacyOptions}>
            <TouchableOpacity
              style={[
                styles.privacyCard,
                privacy === 'public' && styles.selectedPrivacyCard
              ]}
              onPress={() => setPrivacy('public')}
              disabled={isSubmitting}
            >
              <View style={styles.privacyHeader}>
                <View style={[styles.privacyIconContainer, privacy === 'public' && styles.selectedPrivacyIcon]}>
                  <Ionicons 
                    name="globe-outline" 
                    size={24} 
                    color={privacy === 'public' ? '#3EC1F9' : '#666'} 
                  />
                </View>
                <Text style={[
                  styles.privacyTitle,
                  privacy === 'public' && styles.selectedPrivacyText
                ]}>
                  Public Community
                </Text>
                {privacy === 'public' && (
                  <Ionicons name="checkmark-circle" size={20} color="#3EC1F9" />
                )}
              </View>
              <Text style={styles.privacyDescription}>
                Anyone can view and join your community instantly
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.privacyCard,
                privacy === 'private' && styles.selectedPrivacyCard
              ]}
              onPress={() => setPrivacy('private')}
              disabled={isSubmitting}
            >
              <View style={styles.privacyHeader}>
                <View style={[styles.privacyIconContainer, privacy === 'private' && styles.selectedPrivacyIcon]}>
                  <Ionicons 
                    name="lock-closed-outline" 
                    size={24} 
                    color={privacy === 'private' ? '#3EC1F9' : '#666'} 
                  />
                </View>
                <Text style={[
                  styles.privacyTitle,
                  privacy === 'private' && styles.selectedPrivacyText
                ]}>
                  Private Community
                </Text>
                {privacy === 'private' && (
                  <Ionicons name="checkmark-circle" size={20} color="#3EC1F9" />
                )}
              </View>
              <Text style={styles.privacyDescription}>
                You approve each member before they can join
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Enhanced Access Type */}
        <View style={styles.section}>
          <View style={styles.accessTypeHeader}>
            <View>
              <Text style={styles.sectionTitle}>Access Type</Text>
              <Text style={styles.sectionDescription}>
                {isPaid ? 'Monetize your community' : 'Keep it free for everyone'}
              </Text>
            </View>
            <Switch
              value={isPaid}
              onValueChange={(value) => {
                setIsPaid(value);
                if (value) setCurrentStep(Math.max(currentStep, 5));
                if (!value) {
                  setPrice('');
                  setSelectedPaymentMethod(null);
                  setErrors(prev => {
                    const newErrors = { ...prev };
                    delete newErrors.price;
                    delete newErrors.paymentMethod;
                    return newErrors;
                  });
                }
              }}
              trackColor={{ false: '#1A1A1A', true: 'rgba(62, 193, 249, 0.3)' }}
              thumbColor={isPaid ? '#3EC1F9' : '#666'}
              ios_backgroundColor="#1A1A1A"
              disabled={isSubmitting}
            />
          </View>
          
          <View style={[styles.accessTypeCard, isPaid && styles.selectedAccessTypeCard]}>
            <View style={styles.accessTypeInfo}>
              <View style={[styles.accessIconContainer, isPaid && styles.selectedAccessIcon]}>
                <Ionicons 
                  name={isPaid ? 'diamond-outline' : 'people-outline'} 
                  size={24} 
                  color={isPaid ? '#3EC1F9' : '#666'} 
                />
              </View>
              <View style={styles.accessTypeTextContainer}>
                <Text style={[styles.accessTypeTitle, isPaid && styles.selectedAccessTypeText]}>
                  {isPaid ? 'Premium Community' : 'Free Community'}
                </Text>
                <Text style={styles.accessTypeDescription}>
                  {isPaid 
                    ? 'Members pay a monthly fee to access exclusive content' 
                    : 'Anyone can join your community without payment'}
                </Text>
              </View>
            </View>

            {isPaid && (
              <View style={styles.premiumSettings}>
                <View style={styles.priceSection}>
                  <Text style={styles.priceLabel}>Monthly Price</Text>
                  <View style={styles.priceInputContainer}>
                    <Text style={styles.currencySymbol}>$</Text>
                    <TextInput
                      style={[styles.priceInput, errors.price && styles.priceInputError]}
                      placeholder="0.00"
                      placeholderTextColor="#666"
                      value={price}
                      onChangeText={handlePriceChange}
                      keyboardType="decimal-pad"
                      maxLength={8}
                      editable={!isSubmitting}
                    />
                    <Text style={styles.perMonth}>/month</Text>
                  </View>
                  {errors.price && <Text style={styles.errorText}>{errors.price}</Text>}
                </View>

                {priceValue > 0 && (
                  <PaymentMethodSelector
                    onSelect={(method) => {
                      setSelectedPaymentMethod(method);
                      setErrors(prev => {
                        const newErrors = { ...prev };
                        delete newErrors.paymentMethod;
                        return newErrors;
                      });
                    }}
                    price={priceValue}
                    selectedMethod={selectedPaymentMethod}
                  />
                )}
                
                {errors.paymentMethod && (
                  <Text style={styles.errorText}>{errors.paymentMethod}</Text>
                )}
              </View>
            )}
          </View>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: 16,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#1A1A1A',
    backgroundColor: '#000',
  },
  closeButton: {
    padding: 8,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#1A1A1A',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: '#1A1A1A',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3EC1F9',
    borderRadius: 2,
  },
  progressText: {
    color: '#666',
    fontSize: 12,
    marginTop: 4,
  },
  createButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 24,
    backgroundColor: '#3EC1F9',
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#3EC1F9',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  createButtonDisabled: {
    opacity: 0.5,
    shadowOpacity: 0,
    elevation: 0,
  },
  createButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  loadingContainer: {
    height: 20,
    justifyContent: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    lineHeight: 20,
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 4,
  },
  imageUpload: {
    width: '100%',
    height: 180,
    backgroundColor: '#1A1A1A',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#2A2A2A',
    borderStyle: 'dashed',
  },
  imageContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  communityImage: {
    width: '100%',
    height: '100%',
    borderRadius: 14,
  },
  imageEditButton: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageUploadContent: {
    alignItems: 'center',
  },
  imagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  imageUploadText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  imageUploadSubtext: {
    color: '#666',
    fontSize: 14,
  },
  inputContainer: {
    marginBottom: 24,
  },
  labelContainer: {
    marginBottom: 8,
  },
  label: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  required: {
    color: '#FF6B6B',
  },
  inputDescription: {
    color: '#666',
    fontSize: 13,
  },
  inputWrapper: {
    backgroundColor: '#1A1A1A',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  inputWrapperFocused: {
    borderColor: '#3EC1F9',
    backgroundColor: '#1F1F1F',
  },
  inputWrapperError: {
    borderColor: '#FF6B6B',
  },
  input: {
    padding: 16,
    color: '#fff',
    fontSize: 16,
    minHeight: 52,
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  inputFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  errorText: {
    color: '#FF6B6B',
    fontSize: 13,
    flex: 1,
  },
  charCount: {
    color: '#666',
    fontSize: 12,
  },
  charCountError: {
    color: '#FF6B6B',
  },
  categoriesContainer: {
    marginTop: 8,
  },
  categoriesContent: {
    paddingRight: 16,
  },
  categoryCard: {
    width: 160,
    backgroundColor: '#1A1A1A',
    padding: 16,
    borderRadius: 16,
    marginRight: 12,
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
  },
  selectedCategoryCard: {
    borderColor: '#3EC1F9',
    backgroundColor: 'rgba(62, 193, 249, 0.1)',
  },
  categoryIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryIcon: {
    fontSize: 24,
  },
  categoryTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 6,
  },
  categoryDescription: {
    color: '#666',
    fontSize: 12,
    lineHeight: 16,
  },
  selectedIndicator: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  privacyOptions: {
    gap: 12,
  },
  privacyCard: {
    backgroundColor: '#1A1A1A',
    padding: 16,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedPrivacyCard: {
    borderColor: '#3EC1F9',
    backgroundColor: 'rgba(62, 193, 249, 0.1)',
  },
  privacyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  privacyIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2A2A2A',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  selectedPrivacyIcon: {
    backgroundColor: 'rgba(62, 193, 249, 0.2)',
  },
  privacyTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  selectedPrivacyText: {
    color: '#3EC1F9',
  },
  privacyDescription: {
    color: '#666',
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 52,
  },
  accessTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  accessTypeCard: {
    backgroundColor: '#1A1A1A',
    borderRadius: 16,
    padding: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedAccessTypeCard: {
    borderColor: '#3EC1F9',
    backgroundColor: 'rgba(62, 193, 249, 0.1)',
  },
  accessTypeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  accessIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2A2A2A',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  selectedAccessIcon: {
    backgroundColor: 'rgba(62, 193, 249, 0.2)',
  },
  accessTypeTextContainer: {
    flex: 1,
  },
  accessTypeTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  selectedAccessTypeText: {
    color: '#3EC1F9',
  },
  accessTypeDescription: {
    color: '#666',
    fontSize: 14,
    lineHeight: 20,
  },
  premiumSettings: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  priceSection: {
    marginBottom: 20,
  },
  priceLabel: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  currencySymbol: {
    color: '#3EC1F9',
    fontSize: 24,
    fontWeight: 'bold',
    marginRight: 8,
  },
  priceInput: {
    flex: 1,
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    padding: 0,
  },
  priceInputError: {
    color: '#FF6B6B',
  },
  perMonth: {
    color: '#666',
    fontSize: 16,
    marginLeft: 8,
  },
  paymentMethodsContainer: {
    gap: 12,
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedPaymentMethod: {
    borderColor: '#0070BA',
    backgroundColor: 'rgba(0, 112, 186, 0.1)',
  },
  paymentMethodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  paymentIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  paymentMethodInfo: {
    flex: 1,
  },
  paymentMethodText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  selectedPaymentMethodText: {
    color: '#0070BA',
    fontWeight: '600',
  },
  paymentMethodDescription: {
    color: '#555',
    fontSize: 13,
  },
  paymentMethodRight: {
    alignItems: 'flex-end',
  },
  paymentMethodPrice: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  bottomSpacing: {
    height: 40,
  },
});