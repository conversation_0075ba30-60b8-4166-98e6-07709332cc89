import { ImageSourcePropType } from 'react-native';

export interface User {
  id: string;
  name: string;
  handle: string;
  avatar: string | ImageSourcePropType;
}

export interface PostStats {
  likes: number;
  comments: number;
  reposts: number;
}

export interface Reaction {
  type: string;
  count: number;
}

export interface Post {
  id: string;
  user: User;
  content: string;
  timestamp: string;
  image?: string | null;
  performanceIndicator?: string | null;
  stats: PostStats;
  reactions?: Reaction[];
}

// Helper function to generate mock posts
export const generateMockPosts = (count: number, offset: number = 0): Post[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: `post-${i + offset}`,
    user: {
      id: `user-${i + offset}`,
      name: 'User Name',
      handle: `@user${i + offset}`,
      avatar: 'https://via.placeholder.com/40',
    },
    content: `This is a sample post content ${i + 1}.`,
    timestamp: '2h ago',
    image: Math.random() > 0.5 ? 'https://via.placeholder.com/300' : null,
    performanceIndicator: Math.random() > 0.5 ? '+5.2%' : null,
    stats: {
      likes: Math.floor(Math.random() * 100),
      comments: Math.floor(Math.random() * 50),
      reposts: Math.floor(Math.random() * 30),
    },
    reactions: [
      { type: 'like', count: Math.floor(Math.random() * 100) },
      { type: 'laugh', count: Math.floor(Math.random() * 50) },
    ],
  }));
};

export const initialFeedData = generateMockPosts(5);
