import React from 'react';
import { StyleSheet, View, StyleProp, ViewStyle, Text } from 'react-native';
// import WhaleLoader from '../WhaleLoader'; // Removed old loader
import WhaleSpinner from './WhaleSpinner'; // Added new spinner

type LoadingWhaleProps = {
  /**
   * Size of the loading indicator in pixels (width and height)
   * @default 80 (medium size for WhaleSpinner)
   */
  size?: number;
  /**
   * Custom styles for the container
   */
  style?: StyleProp<ViewStyle>;
  /**
   * Color of the loading text (if shown) and fallback ActivityIndicator
   * @default '#3EC1F9'
   */
  color?: string;
  /**
   * Whether to show loading text below the animation
   * @default false
   */
  showText?: boolean;
  /**
   * Custom loading text to display
   */
  text?: string;
  /**
   * Animation speed multiplier (adjust if WhaleSpinner's speed prop has a different meaning)
   * For WhaleSpinner, speed is duration in ms. Let's assume a base speed of 2000ms
   * and adjust with this multiplier. Or, pass it directly if the meaning is equivalent.
   * The issue states 2s per rotation, so speed prop for Whale<PERSON>pinner is duration.
   * Let's pass it directly.
   * @default 2000 (ms for one rotation)
   */
  speed?: number; // This will be passed as duration to <PERSON>hale<PERSON><PERSON><PERSON>
};

/**
 * A loading indicator that displays a spinning whale animation.
 * Uses SVG for smooth, high-quality animations.
 */
const LoadingWhale: React.FC<LoadingWhaleProps> = ({
  size = 80, // Defaulting to a medium size
  style,
  color = '#3EC1F9',
  showText = false,
  text = 'Loading...',
  speed = 2000, // Default duration for WhaleSpinner
}) => {
  return (
    <View style={[styles.container, style]}>
      <WhaleSpinner
        size={size}
        color={color} // Used for fallback in WhaleSpinner
        speed={speed} // Passed as duration to WhaleSpinner
      />
      {showText && (
        <Text style={[styles.loadingText, { color }]}>{text}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1, // Removed flex: 1 to allow component to size itself based on content or parent
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20, // Kept padding for spacing around text and spinner
    backgroundColor: 'transparent',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  // errorText style is not used by WhaleSpinner directly, can be removed if not needed elsewhere
  // errorText: {
  //   marginTop: 16,
  //   fontSize: 14,
  //   textAlign: 'center',
  //   color: '#FF3B30',
  // },
});

export default React.memo(LoadingWhale);
