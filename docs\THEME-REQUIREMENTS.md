# Theme System Requirements

## Current Implementation Analysis

### Tab Navigation Theming

#### Key Findings:
1. **Tab Bar Styling**
   - Hardcoded colors in `_layout.tsx`
   - Custom tab bar with icon-only navigation
   - Active/Inactive state management

2. **Current Implementation**
```typescript
// Current tab bar theming (_layout.tsx)
tabBarStyle: {
  backgroundColor: '#000000',
  borderTopColor: '#222222',
  paddingTop: 10,
  height: 70,
},
tabBarActiveTintColor: '#3EC1F9',
tabBarInactiveTintColor: '#888888',
```

3. **Component-Specific Theming**
   - Each tab has its own style definitions
   - Inconsistent use of theme tokens
   - Mix of inline styles and StyleSheet

#### Component Analysis

1. **Home Screen (index.tsx)**
   - Post cards with reactions and comments
   - Performance indicators with color coding
   - Interactive elements (upvote, downvote, etc.)

2. **Wallet Screen**
   - Exchange connection cards
   - Balance displays
   - Transaction history

3. **Profile Screen**
   - User statistics with charts
   - Achievement badges
   - Portfolio performance

4. **Rankings Screen**
   - Trader leaderboard
   - Performance metrics
   - Timeframe filters

### Existing Theme Structure
- **Light and Dark Themes**: Basic color schemes defined in `app/constants/Colors.ts`
- **Theme Types**: Basic type definitions in `app/(tabs)/community/types/theme.types.ts`
- **Theme Hook**: Simple theme provider using `useColorScheme` in `app/hooks/useTheme.ts`

### Theme Requirements by Component

#### 1. Tab Navigation
```typescript
interface TabBarTheme {
  background: string;
  borderTopColor: string;
  activeTint: string;
  inactiveTint: string;
  height: number;
  padding: {
    top: number;
    bottom: number;
    horizontal: number;
  };
  iconSize: number;
}
```

#### 2. Home Screen Components
```typescript
interface PostTheme {
  card: {
    background: string;
    border: string;
    pressed: string;
  };
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
  reaction: {
    active: string;
    inactive: string;
    background: string;
  };
  performance: {
    positive: string;
    negative: string;
    neutral: string;
  };
}
```

#### 3. Wallet Components
```typescript
interface WalletTheme {
  card: {
    background: string;
    border: string;
    connected: string;
    disconnected: string;
  };
  balance: {
    primary: string;
    secondary: string;
    change: {
      positive: string;
      negative: string;
      neutral: string;
    };
  };
  button: {
    primary: string;
    secondary: string;
    text: string;
    disabled: string;
  };
}
```

#### 4. Profile Components
```typescript
interface ProfileTheme {
  header: {
    background: string;
    text: string;
    statsBackground: string;
  };
  chart: {
    gradientStart: string;
    gradientEnd: string;
    lineColor: string;
    pointColor: string;
    gridColor: string;
    axisColor: string;
  };
  achievement: {
    unlocked: string;
    locked: string;
    background: string;
    border: string;
  };
}
```

### Implementation Strategy

1. **Theme Provider Setup**
   - Create a centralized theme provider
   - Support light/dark/system themes
   - Add theme switching capability

2. **Theme Token System**
   - Define semantic color tokens
   - Create spacing and typography scales
   - Add component-specific tokens

3. **Component Migration**
   - Update components to use theme tokens
   - Add theme-aware styling
   - Implement dark/light mode support

4. **Performance Optimization**
   - Memoize theme-dependent styles
   - Use React.memo for expensive components
   - Optimize re-renders with useCallback/useMemo

### Migration Plan

1. **Phase 1: Foundation**
   - Set up theme provider and context
   - Define base theme tokens
   - Create theme utilities

2. **Phase 2: Core Components**
   - Migrate tab navigation
   - Update shared components
   - Add theme switching UI

3. **Phase 3: Screens**
   - Migrate home screen
   - Update wallet screen
   - Update profile screen
   - Update rankings screen

4. **Phase 4: Polish**
   - Add animations
   - Implement system theme detection
   - Add theme persistence

### Accessibility Considerations

1. **Color Contrast**
   - Ensure WCAG AA/AAA compliance
   - Test with color blindness simulators
   - Provide high contrast mode

2. **Text Scaling**
   - Support dynamic type sizes
   - Test with large text sizes
   - Ensure proper text contrast

3. **Interactive Elements**
   - Minimum touch target sizes
   - Clear focus states
   - Hover/press feedback

1. **Theme Consistency**
   - Inline styles mixed with theme-based styles
   - Hardcoded colors in some components
   - Inconsistent spacing and typography usage

2. **Component-Specific Issues**
   - `CommunityList`: Loading states need better theme integration
   - `CommunityPostItem`: Interactive states not fully themable
   - `CommunityCommentsModal`: Could benefit from theme-based animations
   - `CommunityPostComposer`: Input theming needs enhancement
   - Optional properties not clearly documented

3. **Component-Specific Gaps**
   - `CommunityList`: Needs better loading state theming
   - `CommunityPostItem`: Interactive states (pressed, selected) need theme support
   - `CommunityCommentsModal`: Should support custom theming for different contexts
   - `CommunityPostComposer`: Needs better theming for input states

4. **Performance Considerations**
   - Theme objects are recreated on each render in some components
   - No memoization of theme-dependent styles
   - Inefficient style recalculations on theme change

## Theme System Requirements

### 1. Core Theme Properties

#### Community-Specific Extensions
```typescript
interface CommunityThemeExtensions {
  community: {
    card: {
      background: string;
      pressed: string;
      border: string;
    };
    post: {
      background: string;
      header: string;
      footer: string;
      actionButton: string;
      actionButtonActive: string;
    };
    comment: {
      background: string;
      nestedBackground: string;
      border: string;
    };
    input: {
      background: string;
      text: string;
      placeholder: string;
      border: string;
      focused: string;
    };
  };
}

type AppTheme = Theme & CommunityThemeExtensions;
```

#### Component-Specific Theme Hooks
```typescript
// Example for CommunityList
const useCommunityListStyles = (theme: AppTheme) => {
  return useMemo(() => ({
    container: {
      backgroundColor: theme.background,
    },
    list: {
      backgroundColor: theme.community.card.background,
    },
    // ... other styles
  }), [theme]);
};

// Example for CommunityPostItem
const usePostItemStyles = (theme: AppTheme, isPressed: boolean) => {
  return useMemo(() => ({
    container: {
      backgroundColor: isPressed 
        ? theme.community.post.pressed 
        : theme.community.post.background,
      borderColor: theme.border,
      // ... other styles
    },
    // ... other style objects
  }), [theme, isPressed]);
};
```

#### Color System
- **Primary Colors**: Brand colors and variants
- **Semantic Colors**: Success, error, warning, info
- **Background Colors**: Primary, secondary, tertiary
- **Text Colors**: Primary, secondary, disabled, inverse
- **Border Colors**: Default, focused, error
- **Status Colors**: Online, away, busy, offline
- **Surface Colors**: Cards, modals, sheets

#### Typography
- Font families
- Font sizes (heading levels, body, caption, etc.)
- Font weights
- Line heights
- Letter spacing

#### Spacing System
- Base unit (e.g., 4px)
- Scale factors
- Padding/margin variants

#### Border Radius
- Small, medium, large, full
- Component-specific radii

#### Shadows
- Elevation levels (0-24)
- Custom shadows for components

### 2. Theme Modes
- Light mode (default)
- Dark mode
- High contrast mode (accessibility)
- System preference detection

### 3. Component-Specific Theming
- Buttons (primary, secondary, text, outlined, etc.)
- Input fields
- Cards
- Navigation
- Alerts and toasts
- Avatars
- Badges
- Chips
- Progress indicators

### 4. Animation
- Duration constants
- Easing functions
- Transition presets

### 5. Breakpoints
- Mobile, tablet, desktop
- Responsive design tokens

## Implementation Strategy

### 1. Theme Provider
```typescript
interface ThemeProviderProps {
  theme?: 'light' | 'dark' | 'system';
  customThemes?: Record<string, Theme>;
  children: React.ReactNode;
}
```

### 2. Theme Structure
```typescript
interface Theme {
  colors: {
    primary: string;
    secondary: string;
    background: {
      primary: string;
      secondary: string;
      tertiary: string;
    };
    text: {
      primary: string;
      secondary: string;
      disabled: string;
      inverse: string;
    };
    // ... other color tokens
  };
  typography: {
    fontFamily: string;
    fontSize: {
      h1: number;
      h2: number;
      // ... other font sizes
    };
    // ... other typography tokens
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    // ... other spacing tokens
  };
  // ... other theme categories
}
```

### 3. Migration Path
1. Create comprehensive theme tokens
2. Implement theme provider with context
3. Create theme utilities and hooks
4. Migrate components one by one
5. Add theme switching functionality
6. Add documentation and examples

## Next Steps

1. **Design Token Audit**
   - Inventory all current colors, typography, and spacing
   - Identify inconsistencies

2. **Theme Implementation**
   - Set up theme provider
   - Create theme utilities
   - Implement theme switching

3. **Component Migration**
   - Migrate components to use theme tokens
   - Ensure accessibility compliance
   - Test across themes

4. **Documentation**
   - Document theme structure
   - Create usage examples
   - Add theme guidelines for new components

## Timeline

1. **Week 1-2**: Design token audit and theme structure definition
2. **Week 3-4**: Theme provider implementation and core utilities
3. **Week 5-6**: Component migration (priority components first)
4. **Week 7-8**: Testing, documentation, and final adjustments

## Dependencies

- React Native
- React Navigation (for navigation theming)
- Optional: styled-components or similar for theme utilities

## Testing Requirements

- Visual regression testing for theme changes
- Accessibility testing (color contrast, etc.)
- Cross-platform consistency
- Performance impact assessment

## Success Metrics

- 100% of components using theme tokens
- Consistent theming across all screens
- Smooth theme transitions
- No visual regressions
- Improved accessibility scores
