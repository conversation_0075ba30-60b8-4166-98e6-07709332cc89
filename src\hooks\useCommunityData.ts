import { useState, useEffect, useCallback } from 'react';

// Type definitions
interface Reaction {
  emoji: string;
  count: number;
  isActive: boolean;
}

interface Comment {
  id: string;
  author: string;
  avatar: string;
  content: string;
  timestamp: string;
  likes: number;
  isLiked: boolean;
  isModerator?: boolean;
}

interface CommunityPost {
  id: string;
  author: string;
  handle: string;
  avatar: string;
  performance?: string;
  performanceType?: 'positive' | 'negative';
  date: string;
  title: string;
  content: string;
  reactions: Reaction[];
  upvotes: number;
  downvotes: number;
  comments: Comment[];
  isUpvoted: boolean;
  isDownvoted: boolean;
  showComments?: boolean;
  newComment?: string;
  isPinned?: boolean;
  memberOnly?: boolean;
  moderatorPost?: boolean;
}

interface UseCommunityDataReturn {
  posts: CommunityPost[] | null;
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  error: string | null;
  refreshPosts: () => Promise<void>;
  loadMorePosts: () => Promise<void>;
  addPost: (post: CommunityPost) => Promise<void>;
  updatePost: (postId: string, updates: Partial<CommunityPost>) => Promise<void>;
  joinCommunity: () => Promise<void>;
  leaveCommunity: () => Promise<void>;
  refreshData: () => Promise<void>;
}

// Generate community posts with unique IDs and enhanced content
const generateCommunityPosts = (communityId: string, count: number, offset: number = 0): CommunityPost[] => {
  const posts: CommunityPost[] = [];
  const postTemplates = [
    { title: 'Weekly Market Analysis', content: 'Here\'s my comprehensive take on this week\'s market movements. The charts are showing some interesting patterns that could indicate a potential breakout. What are your thoughts on the current support levels?' },
    { title: 'New Protocol Launch', content: 'Exciting news! A new protocol just launched in our ecosystem with some innovative features. The tokenomics look solid and the team has a strong track record. Early adopters might want to DYOR.' },
    { title: 'Community AMA Announcement', content: 'Join us for an exclusive AMA session with the core team this Friday at 3 PM UTC. We\'ll be discussing upcoming roadmap updates, partnerships, and answering your burning questions!' },
    { title: 'Technical Discussion', content: 'Let\'s dive deep into the technical aspects of the latest upgrade. The new consensus mechanism promises 50% faster transaction times while maintaining security. Developers, what\'s your take?' },
    { title: 'Success Story', content: 'Sharing my journey from crypto newbie to profitable trader. It took 2 years of learning, countless mistakes, but the community support here was invaluable. Here are my top 5 lessons learned...' },
    { title: 'Market Alert', content: 'Major resistance level approaching at $45K. Volume is picking up and we might see a decisive move in the next 24-48 hours. Keep your eyes on the charts!' },
    { title: 'Educational Content', content: 'For newcomers: Understanding DeFi yield farming basics. This thread will cover liquidity pools, impermanent loss, and risk management strategies. Bookmark this for later!' },
    { title: 'Community Poll', content: 'What\'s your prediction for the next major catalyst? A) ETF approval B) Major adoption news C) Technical breakthrough D) Regulatory clarity. Vote and share your reasoning!' },
    { title: 'Partnership Announcement', content: 'Huge news! We\'re partnering with a major institutional player to bring more liquidity to our ecosystem. This could be a game-changer for price discovery and stability.' },
    { title: 'Risk Management Tips', content: 'Reminder: Never invest more than you can afford to lose. With recent volatility, it\'s crucial to have proper position sizing and stop-losses in place. Protect your capital first!' }
  ];

  const authors = [
    { name: 'Alex_Crypto_123', handle: '@alexcrypto123', avatar: 'https://i.pravatar.cc/150?img=1' },
    { name: 'Sarah_DeFi_Pro', handle: '@sarahdefipro', avatar: 'https://i.pravatar.cc/150?img=2' },
    { name: 'Mike_Trader_456', handle: '@miketrader456', avatar: 'https://i.pravatar.cc/150?img=3' },
    { name: 'Luna_Analyst_789', handle: '@lunaanalyst789', avatar: 'https://i.pravatar.cc/150?img=4' },
    { name: 'Bob_HODL_Master', handle: '@bobhodlmaster', avatar: 'https://i.pravatar.cc/150?img=5' },
    { name: 'Emma_Chain_Dev', handle: '@emmachaindev', avatar: 'https://i.pravatar.cc/150?img=6' },
    { name: 'Jake_Yield_Farm', handle: '@jakeyieldfarm', avatar: 'https://i.pravatar.cc/150?img=7' },
    { name: 'Zoe_NFT_Queen', handle: '@zoenftqueen', avatar: 'https://i.pravatar.cc/150?img=8' }
  ];

  for (let i = 0; i < count; i++) {
    const template = postTemplates[i % postTemplates.length];
    const isPinned = i === 0 && Math.random() > 0.7;
    const isModerator = Math.random() > 0.8;
    const author = isModerator ?
      { name: 'Community_Mod', handle: '@communitymod', avatar: 'https://i.pravatar.cc/150?img=99' } :
      authors[(i + offset) % authors.length];

    // Generate unique ID using timestamp and random component
    const uniqueId = `${communityId}-post-${Date.now()}-${Math.random().toString(36).substr(2, 9)}-${i + offset}`;

    posts.push({
      id: uniqueId,
      author: author.name,
      handle: author.handle,
      avatar: author.avatar,
      performance: Math.random() > 0.6 ? `+${(Math.random() * 25 + 1).toFixed(2)}%` : Math.random() > 0.3 ? `-${(Math.random() * 15 + 0.5).toFixed(2)}%` : undefined,
      performanceType: Math.random() > 0.5 ? 'positive' : 'negative',
      date: new Date(Date.now() - (i + offset) * 86400000 * Math.random() * 7).toLocaleDateString(),
      title: template.title,
      content: template.content,
      reactions: [
        { emoji: '🚀', count: Math.floor(Math.random() * 80) + 10, isActive: false },
        { emoji: '💎', count: Math.floor(Math.random() * 60) + 5, isActive: false },
        { emoji: '📈', count: Math.floor(Math.random() * 40) + 3, isActive: false },
        { emoji: '🔥', count: Math.floor(Math.random() * 30) + 2, isActive: false },
        { emoji: '👏', count: Math.floor(Math.random() * 25) + 1, isActive: false },
      ],
      upvotes: Math.floor(Math.random() * 300) + 20,
      downvotes: Math.floor(Math.random() * 30) + 1,
      comments: [],
      isUpvoted: false,
      isDownvoted: false,
      isPinned,
      memberOnly: Math.random() > 0.85,
      moderatorPost: isModerator,
    });
  }

  return posts;
};

export function useCommunityData(communityId: string, isJoined: boolean): UseCommunityDataReturn {
  const [posts, setPosts] = useState<CommunityPost[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize posts
  useEffect(() => {
    const initializePosts = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        const communityPosts = generateCommunityPosts(communityId, 10);
        setPosts(communityPosts);
      } catch (err) {
        setError('Failed to load posts');
        console.error('Error loading posts:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (communityId) {
      initializePosts();
    }
  }, [communityId]);

  const refreshPosts = useCallback(async () => {
    setIsRefreshing(true);
    setError(null);
    try {
      // Simulate API refresh
      await new Promise(resolve => setTimeout(resolve, 1500));
      const communityPosts = generateCommunityPosts(communityId, 10);
      setPosts(communityPosts);
    } catch (err) {
      setError('Failed to refresh posts');
      console.error('Error refreshing posts:', err);
    } finally {
      setIsRefreshing(false);
    }
  }, [communityId]);

  const loadMorePosts = useCallback(async () => {
    if (isLoadingMore || !posts) return;

    setIsLoadingMore(true);
    try {
      // Simulate loading more data
      await new Promise(resolve => setTimeout(resolve, 1000));
      const currentPostCount = posts.length;
      const morePosts = generateCommunityPosts(communityId, 5, currentPostCount);
      setPosts(prevPosts => [...(prevPosts || []), ...morePosts]);
    } catch (err) {
      setError('Failed to load more posts');
      console.error('Error loading more posts:', err);
    } finally {
      setIsLoadingMore(false);
    }
  }, [communityId, posts, isLoadingMore]);

  const addPost = useCallback(async (post: CommunityPost) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setPosts(prevPosts => [post, ...(prevPosts || [])]);
    } catch (err) {
      setError('Failed to add post');
      console.error('Error adding post:', err);
      throw err;
    }
  }, []);

  const updatePost = useCallback(async (postId: string, updates: Partial<CommunityPost>) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setPosts(prevPosts => 
        prevPosts?.map(post => 
          post.id === postId ? { ...post, ...updates } : post
        ) || null
      );
    } catch (err) {
      setError('Failed to update post');
      console.error('Error updating post:', err);
      throw err;
    }
  }, []);

  const joinCommunity = useCallback(async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Handle join logic
    } catch (err) {
      setError('Failed to join community');
      console.error('Error joining community:', err);
      throw err;
    }
  }, []);

  const leaveCommunity = useCallback(async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Handle leave logic
    } catch (err) {
      setError('Failed to leave community');
      console.error('Error leaving community:', err);
      throw err;
    }
  }, []);

  const refreshData = useCallback(async () => {
    await refreshPosts();
  }, [refreshPosts]);

  return {
    posts,
    isLoading,
    isRefreshing,
    isLoadingMore,
    error,
    refreshPosts,
    loadMorePosts,
    addPost,
    updatePost,
    joinCommunity,
    leaveCommunity,
    refreshData,
  };
}
