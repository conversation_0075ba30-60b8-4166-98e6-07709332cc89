import React, { useState, useCallback, useEffect, useRef } from 'react';
import { 
  SafeAreaView, 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  TouchableOpacity, 
  FlatList, 
  RefreshControl,
  TextInput,
  Keyboard,
  Animated,
  Share,
  Modal,
  KeyboardAvoidingView,
  Platform,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
// Import with proper error handling
import { useLoadingStates } from '../../hooks/useLoadingStates';
import { LoadingState } from '../../components/LoadingState';
import Header from '../../components/Header';

interface Reaction {
  emoji: string;
  count: number;
  isActive: boolean;
}

interface Comment {
  id: string;
  author: string;
  avatar: string;
  content: string;
  timestamp: string;
  likes: number;
  isLiked: boolean;
}

interface Post {
  id: string;
  author: string;
  handle: string;
  avatar: string;
  performance: string;
  performanceType: 'positive' | 'negative';
  date: string;
  title: string;
  content: string;
  reactions: Reaction[];
  upvotes: number;
  downvotes: number;
  comments: Comment[];
  isUpvoted: boolean;
  isDownvoted: boolean;
  isFollowing: boolean;
  showComments?: boolean;
  newComment?: string;
}

// List of realistic traders with diverse names and avatars
const TRADERS = [
  {
    name: 'Alex Thompson',
    handle: '@cryptoalex',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
    isFollowing: true,
  },
  {
    name: 'Sarah Chen',
    handle: '@tradingpro',
    avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400',
    isFollowing: true,
  },
  {
    name: 'Mike Rodriguez',
    handle: '@cryptomike',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
    isFollowing: false,
  },
  {
    name: 'James Wilson',
    handle: '@jamesw',
    avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400',
    isFollowing: true,
  },
  {
    name: 'Emma Johnson',
    handle: '@emmaj',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',
    isFollowing: false,
  },
  {
    name: 'David Kim',
    handle: '@davidk',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400',
    isFollowing: true,
  },
  {
    name: 'Olivia Martinez',
    handle: '@oliviam',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400',
    isFollowing: false,
  },
  {
    name: 'Michael Brown',
    handle: '@mikeb',
    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400',
    isFollowing: false,
  },
  {
    name: 'Sophia Garcia',
    handle: '@sophiag',
    avatar: 'https://images.unsplash.com/photo-1544723795-3fb6469f5b39?w=400',
    isFollowing: true,
  },
  {
    name: 'Daniel Lee',
    handle: '@daniell',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
    isFollowing: false,
  },
];

// List of possible post titles and contents
const POST_TITLES = [
  'Market Analysis: Current Trends',
  'New Project Alert: Detailed Review',
  'Technical Analysis: Breaking Down the Charts',
  'Fundamental Analysis: Long-term Potential',
  'Trading Strategy: My Approach',
  'Market Sentiment Update',
  'Portfolio Rebalancing Strategy',
  'Upcoming Events in Crypto',
  'Regulatory Update: What It Means',
  'Yield Farming Opportunities',
];

const POST_CONTENTS = [
  'The market is showing strong bullish signals across multiple timeframes. Key resistance levels are being tested...',
  'Just discovered this new project with an innovative approach to DeFi. The team has a solid track record...',
  'The recent price action suggests we might be in for a significant move. Here\'s what the indicators are showing...',
  'After analyzing the fundamentals, I believe this asset is significantly undervalued. Here\'s why...',
  'My trading strategy has evolved over the years. Here\'s my current approach to managing risk...',
  'Market sentiment is shifting rapidly. Here are the key metrics I\'m watching...',
  'It\'s time to rebalance the portfolio. Here\'s my strategy for the coming quarter...',
  'Several major events are coming up that could impact the market. Here\'s what you need to know...',
  'The latest regulatory developments could have significant implications. Here\'s my analysis...',
  'Found some interesting yield farming opportunities with great APYs. Let me break them down...',
];

// Generate mock posts for different feeds
const generateMockPosts = (followingOnly: boolean = false): Post[] => {
  const filteredTraders = followingOnly ? TRADERS.filter(trader => trader.isFollowing) : TRADERS;
  
  const generateComments = (count: number, postId: string): Comment[] => {
    return Array.from({ length: Math.min(count, 5) }, (_, i) => ({
      id: `comment-${postId}-${i}`,
      author: i === 0 ? 'You' : TRADERS[i % TRADERS.length].name,
      avatar: i === 0 
        ? 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400' 
        : TRADERS[i % TRADERS.length].avatar,
      content: i === 0 
        ? 'Great analysis! I agree with your points.'
        : ['Interesting take!', 'Thanks for sharing!', 'What about the RSI?', 'I think we might see a pullback first.'][i % 4],
      timestamp: i === 0 ? 'Just now' : `${i * 2}h ago`,
      likes: Math.floor(Math.random() * 20),
      isLiked: i === 0 ? false : Math.random() > 0.7
    }));
  };

  return [
    {
      id: '1',
      author: 'Alex Thompson',
      handle: '@cryptoalex',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
      performance: '+23.50%',
      performanceType: 'positive',
      date: new Date().toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
      title: 'New NFT Project Launch Analysis',
      content: 'Important market update: Multiple indicators suggesting a trend reversal in the NFT space. The new project has strong fundamentals and an experienced team.',
      reactions: [
        { emoji: '🚀', count: 24, isActive: false },
        { emoji: '📈', count: 18, isActive: false },
        { emoji: '💎', count: 12, isActive: false },
        { emoji: '😮', count: 8, isActive: false },
      ],
      upvotes: 660,
      downvotes: 28,
      comments: generateComments(42, '1'),
      isUpvoted: false,
      isDownvoted: false,
      isFollowing: true,
      showComments: false,
      newComment: ''
    },
    {
      id: '2',
      author: 'Sarah Chen',
      handle: '@tradingpro',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400',
      performance: '+15.20%',
      performanceType: 'positive',
      date: new Date(Date.now() - 86400000).toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
      title: 'DeFi Market Analysis',
      content: 'The latest developments in DeFi are showing promising signs. TVL is up, and several protocols are announcing major upgrades. Here\'s my take on the current state of the market and where we might be heading next.',
      reactions: [
        { emoji: '💡', count: 15, isActive: false },
        { emoji: '🎯', count: 9, isActive: false },
        { emoji: '⚡', count: 7, isActive: false },
      ],
      upvotes: 452,
      downvotes: 12,
      comments: generateComments(56, '2'),
      isUpvoted: false,
      isDownvoted: false,
      isFollowing: true,
      showComments: false,
      newComment: ''
    },
    ...(followingOnly ? [] : [{
      id: '3',
      author: 'Mike Rodriguez',
      handle: '@cryptomike',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
      performance: '-5.80%',
      performanceType: 'negative' as const,
      date: new Date(Date.now() - 172800000).toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
      title: 'Market Correction Analysis',
      content: 'Today\'s dip presents an interesting opportunity for long-term holders. While short-term volatility is expected, the fundamentals remain strong. I\'m using this as a buying opportunity.',
      reactions: [
        { emoji: '📉', count: 22, isActive: false },
        { emoji: '💪', count: 13, isActive: false },
        { emoji: '🤔', count: 8, isActive: false },
      ],
      upvotes: 324,
      downvotes: 45,
      comments: generateComments(67, '3'),
      isUpvoted: false,
      isDownvoted: false,
      isFollowing: false,
      showComments: false,
      newComment: ''
    }])
  ];
};

// Generate more mock posts for pagination
const generateMorePosts = (startIndex: number, count: number, followingOnly: boolean = false): Post[] => {
  const currentDate = new Date();
  const filteredTraders = followingOnly ? TRADERS.filter(trader => trader.isFollowing) : TRADERS;
  
  if (filteredTraders.length === 0) return [];
  
  return Array.from({ length: count }, (_, i) => {
    const traderIndex = (startIndex + i) % filteredTraders.length;
    const uniqueSuffix = Math.floor((startIndex + i) / filteredTraders.length);
    const trader = filteredTraders[traderIndex];
    const isPositive = Math.random() > 0.3; // 70% chance of positive performance
    const performance = isPositive 
      ? `+${(Math.random() * 20).toFixed(2)}%` 
      : `-${(Math.random() * 10).toFixed(2)}%`;
    
    // Generate a random date within the last 30 days
    const postDate = new Date();
    postDate.setDate(currentDate.getDate() - Math.floor(Math.random() * 30));
    
    const comments: Comment[] = Array.from({ length: Math.min(Math.floor(Math.random() * 10), 3) }, (_, j) => ({
      id: `comment-${startIndex + i}-${j}`,
      author: j === 0 ? 'You' : TRADERS[(traderIndex + j) % TRADERS.length].name,
      avatar: j === 0 ? 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400' : TRADERS[(traderIndex + j) % TRADERS.length].avatar,
      content: j === 0 
        ? 'Great analysis! I agree with your points.'
        : ['Interesting take!', 'Thanks for sharing!', 'What about the RSI?', 'I think we might see a pullback first.'][j % 4],
      timestamp: j === 0 ? 'Just now' : `${j * 2}h ago`,
      likes: Math.floor(Math.random() * 20),
      isLiked: j === 0 ? false : Math.random() > 0.7
    }));
    
    return {
      id: `post-${startIndex + i}`,
      author: uniqueSuffix > 0 ? `${trader.name} ${uniqueSuffix + 1}` : trader.name,
      handle: uniqueSuffix > 0 ? `${trader.handle}${uniqueSuffix + 1}` : trader.handle,
      avatar: trader.avatar,
      performance,
      performanceType: isPositive ? 'positive' : 'negative',
      date: postDate.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
      title: POST_TITLES[Math.floor(Math.random() * POST_TITLES.length)],
      content: POST_CONTENTS[Math.floor(Math.random() * POST_CONTENTS.length)],
      reactions: [
        { emoji: '🚀', count: Math.floor(Math.random() * 50), isActive: false },
        { emoji: '📈', count: Math.floor(Math.random() * 30), isActive: false },
        { emoji: '💎', count: Math.floor(Math.random() * 20), isActive: false },
      ],
      upvotes: Math.floor(Math.random() * 1000) + 100,
      downvotes: Math.floor(Math.random() * 50),
      comments,
      isUpvoted: false,
      isDownvoted: false,
      isFollowing: trader.isFollowing,
      showComments: false,
      newComment: ''
    };
  });
};

export default function HomeScreen() {
  // State for comment modal
  const [commentModalVisible, setCommentModalVisible] = useState(false);
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [posts, setPosts] = useState<Post[]>([]);
  const [activeTab, setActiveTab] = useState<'for-you' | 'following'>('for-you');
  const [error, setError] = useState<string | null>(null);
  const [isTabLoading, setIsTabLoading] = useState(false);
  
  // New state for post composition
  const [isComposing, setIsComposing] = useState(false);
  const [postText, setPostText] = useState('');
  const [isPosting, setIsPosting] = useState(false);
  const inputRef = useRef<TextInput>(null);
  const inputHeight = useRef(new Animated.Value(50)).current;
  
  // Use loading states with fallback
  const loadingStates = useLoadingStates();

  const {
    isInitialLoading,
    isRefreshing,
    isLoadingMore,
    setIsInitialLoading,
    setIsRefreshing,
    setIsLoadingMore
  } = loadingStates;
  
  // Load data when tab changes or on initial load
  useEffect(() => {
    const loadDataForTab = async () => {
      try {
        setError(null);
        
        // Use different loading state for initial load vs tab switch
        if (posts.length === 0) {
          setIsInitialLoading(true);
        } else {
          setIsTabLoading(true); // Only show feed loading, not full screen
        }
        
        await new Promise(resolve => setTimeout(resolve, 500)); // Reduced delay for tab switches
        const isFollowingTab = activeTab === 'following';
        setPosts(generateMockPosts(isFollowingTab));
      } catch (err) {
        setError('Failed to load posts. Please try again.');
        console.error('Error loading posts:', err);
      } finally {
        setIsInitialLoading(false);
        setIsTabLoading(false);
      }
    };
    
    loadDataForTab();
  }, [activeTab, setIsInitialLoading]); // Added setIsInitialLoading to deps

  const loadMorePosts = useCallback(async () => {
    if (isLoadingMore || isRefreshing || isTabLoading) return;
    
    try {
      setIsLoadingMore(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      const isFollowingTab = activeTab === 'following';
      const newPosts = generateMorePosts(posts.length, 5, isFollowingTab);
      setPosts(prevPosts => [...prevPosts, ...newPosts]);
    } catch (err) {
      setError('Failed to load more posts. Please try again.');
      console.error('Error loading more posts:', err);
    } finally {
      setIsLoadingMore(false);
    }
  }, [posts.length, isLoadingMore, isRefreshing, activeTab, isTabLoading, setIsLoadingMore]);

  const onRefresh = useCallback(async () => {
    if (isRefreshing || isTabLoading) return;
    
    try {
      setError(null);
      setIsRefreshing(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      const isFollowingTab = activeTab === 'following';
      setPosts(generateMockPosts(isFollowingTab));
    } catch (err) {
      setError('Failed to refresh posts. Please try again.');
      console.error('Error refreshing posts:', err);
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing, activeTab, isTabLoading, setIsRefreshing]);
  
  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && !isRefreshing && !isTabLoading) {
      loadMorePosts();
    }
  }, [isLoadingMore, isRefreshing, loadMorePosts, isTabLoading]);

  const handleTabChange = useCallback((tab: 'for-you' | 'following') => {
    if (tab !== activeTab && !isTabLoading) {
      setActiveTab(tab);
      // Data will be loaded via useEffect
    }
  }, [activeTab, isTabLoading]);

  // New handlers for post composition
  const handleInputFocus = useCallback(() => {
    setIsComposing(true);
    Animated.timing(inputHeight, {
      toValue: 120,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [inputHeight]);

  const handleInputBlur = useCallback(() => {
    if (postText.trim() === '') {
      setIsComposing(false);
      Animated.timing(inputHeight, {
        toValue: 50,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  }, [postText, inputHeight]);

  const handlePost = useCallback(async () => {
    if (postText.trim() === '' || isPosting) return;
    
    setIsPosting(true);
    try {
      // Simulate posting delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Create new post
      const newPost: Post = {
        id: `user-post-${Date.now()}`,
        author: 'You', // In a real app, this would be the current user's name
        handle: '@you',
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=400', // Default user avatar
        performance: '+0.00%',
        performanceType: 'positive',
        date: new Date().toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' }),
        title: 'New Post',
        content: postText.trim(),
        reactions: [
          { emoji: '🚀', count: 0, isActive: false },
          { emoji: '📈', count: 0, isActive: false },
          { emoji: '💎', count: 0, isActive: false },
        ],
        upvotes: 0,
        downvotes: 0,
        comments: [],
        isUpvoted: false,
        isDownvoted: false,
        isFollowing: false,
        showComments: false,
        newComment: ''
      };
      
      // Add the new post to the top of the feed
      setPosts(prevPosts => [newPost, ...prevPosts]);
      
      // Reset the input
      setPostText('');
      setIsComposing(false);
      inputRef.current?.blur();
      
      Animated.timing(inputHeight, {
        toValue: 50,
        duration: 200,
        useNativeDriver: false,
      }).start();
      
    } catch (err) {
      setError('Failed to post. Please try again.');
      console.error('Error posting:', err);
    } finally {
      setIsPosting(false);
    }
  }, [postText, isPosting, inputHeight]);

  const handleCancelCompose = useCallback(() => {
    setPostText('');
    setIsComposing(false);
    inputRef.current?.blur();
    
    Animated.timing(inputHeight, {
      toValue: 50,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [inputHeight]);

  const handleReaction = useCallback((postId: string, reactionIndex: number) => {
    setPosts(prevPosts => 
      prevPosts.map(post => 
        post.id === postId 
          ? {
              ...post,
              reactions: post.reactions.map((reaction, index) => 
                index === reactionIndex 
                  ? { 
                      ...reaction, 
                      count: reaction.isActive ? reaction.count - 1 : reaction.count + 1,
                      isActive: !reaction.isActive 
                    }
                  : reaction
              )
            }
          : post
      )
    );
  }, []);

  const handleVote = useCallback((postId: string, type: 'up' | 'down') => {
    setPosts(prevPosts => 
      prevPosts.map(post => 
        post.id === postId 
          ? {
              ...post,
              upvotes: type === 'up' 
                ? post.isUpvoted ? post.upvotes - 1 : post.upvotes + 1
                : post.isDownvoted && type === 'down' ? post.upvotes : post.upvotes,
              downvotes: type === 'down' 
                ? post.isDownvoted ? post.downvotes - 1 : post.downvotes + 1  
                : post.isUpvoted && type === 'up' ? post.downvotes : post.downvotes,
              isUpvoted: type === 'up' ? !post.isUpvoted : false,
              isDownvoted: type === 'down' ? !post.isDownvoted : false,
            }
          : post
      )
    );
  }, []);

  const handleShare = async (post: Post) => {
    try {
      await Share.share({
        message: `${post.title}\n\n${post.content}\n\nShared via HodlHub`,
        title: `Post by ${post.author}`
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const toggleComments = (post: Post) => {
    setSelectedPost(post);
    setCommentModalVisible(true);
  };

  const handleAddComment = () => {
    if (!selectedPost?.newComment?.trim()) return;

    const newComment: Comment = {
      id: Date.now().toString(),
      author: 'You',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
      content: selectedPost.newComment.trim(),
      timestamp: 'Just now',
      likes: 0,
      isLiked: false
    };

    setPosts(prevPosts => 
      prevPosts.map(post => 
        post.id === selectedPost.id
          ? {
              ...post,
              comments: [...post.comments, newComment],
              newComment: ''
            }
          : post
      )
    );

    setSelectedPost(prev => 
      prev ? {
        ...prev,
        comments: [...prev.comments, newComment],
        newComment: ''
      } : null
    );
    
    // Dismiss the keyboard after posting a comment
    Keyboard.dismiss();
  };

  const handleLikeComment = (commentId: string) => {
    if (!selectedPost) return;
    
    const updatedComments = selectedPost.comments.map(comment => 
      comment.id === commentId
        ? {
            ...comment,
            likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1,
            isLiked: !comment.isLiked
          }
        : comment
    );

    setSelectedPost({
      ...selectedPost,
      comments: updatedComments
    });

    setPosts(prevPosts => 
      prevPosts.map(post => 
        post.id === selectedPost.id
          ? { ...post, comments: updatedComments }
          : post
      )
    );
  };

  const renderPost = useCallback(({ item }: { item: Post }) => (
    <View style={styles.postContainer}>
      {/* Post Header */}
      <View style={styles.postHeader}>
        <Image source={{ uri: item.avatar }} style={styles.avatar} />
        <View style={styles.userInfo}>
          <View style={styles.userNameRow}>
            <Text style={styles.authorName}>{item.author}</Text>
            <Text style={styles.handle}>{item.handle}</Text>
            <Text style={[
              styles.performance, 
              { color: item.performanceType === 'positive' ? '#4CAF50' : '#F44336' }
            ]}>
              {item.performance}
            </Text>
          </View>
          <Text style={styles.date}>{item.date}</Text>
        </View>
      </View>

      {/* Post Content */}
      <Text style={styles.postTitle}>{item.title}</Text>
      <Text style={styles.postContent}>{item.content}</Text>

      {/* Reactions */}
      <View style={styles.reactionsContainer}>
        {item.reactions.map((reaction, index) => (
          <TouchableOpacity 
            key={index}
            style={[styles.reactionButton, reaction.isActive && styles.reactionActive]}
            onPress={() => handleReaction(item.id, index)}
          >
            <Text style={styles.reactionEmoji}>{reaction.emoji}</Text>
            <Text style={styles.reactionCount}>{reaction.count}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Post Actions - Full Width */}
      <View style={styles.postActions}>
        <TouchableOpacity 
          style={styles.voteButton}
          onPress={() => handleVote(item.id, 'up')}
        >
          <Ionicons 
            name={item.isUpvoted ? "arrow-up" : "arrow-up-outline"} 
            size={20} 
            color={item.isUpvoted ? "#4CAF50" : "#888"} 
          />
          <Text style={[styles.voteText, item.isUpvoted && { color: "#4CAF50" }]}>
            {item.upvotes}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.voteButton}
          onPress={() => handleVote(item.id, 'down')}
        >
          <Ionicons 
            name={item.isDownvoted ? "arrow-down" : "arrow-down-outline"} 
            size={20} 
            color={item.isDownvoted ? "#F44336" : "#888"} 
          />
          <Text style={[styles.voteText, item.isDownvoted && { color: "#F44336" }]}>
            {item.downvotes}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => toggleComments(item)}
        >
          <Ionicons name="chatbubble-outline" size={20} color="#888" />
          <Text style={styles.actionText}>{item.comments?.length || 0}</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => handleShare(item)}
        >
          <Ionicons name="share-outline" size={20} color="#888" />
        </TouchableOpacity>
      </View>
    </View>
  ), [handleReaction, handleVote]);

  const renderComment = ({ item }: { item: Comment }) => (
    <View style={styles.commentContainer}>
      <Image source={{ uri: item.avatar }} style={styles.commentAvatar} />
      <View style={styles.commentContent}>
        <View style={styles.commentHeader}>
          <Text style={styles.commentAuthor}>{item.author}</Text>
          <Text style={styles.commentTime}>{item.timestamp}</Text>
        </View>
        <Text style={styles.commentText}>{item.content}</Text>
        <View style={styles.commentActions}>
          <TouchableOpacity 
            style={styles.commentAction}
            onPress={() => handleLikeComment(item.id)}
          >
            <Ionicons 
              name={item.isLiked ? "heart" : "heart-outline"} 
              size={16} 
              color={item.isLiked ? "#F44336" : "#888"} 
            />
            {item.likes > 0 && (
              <Text style={[styles.commentActionText, item.isLiked && { color: "#F44336" }]}>
                {item.likes}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const LoadingComponent = useCallback(() => {
    return <LoadingState size={40} />;
  }, []);

  const FullScreenLoader = useCallback(() => {
    return <LoadingState size={80} fullScreen />;
  }, []);

  return (
    <React.Fragment>
      <Stack.Screen options={{ headerShown: false }} />
      <SafeAreaView style={styles.container}>
        <Header />
        
        {/* Comments Modal */}
        <Modal
          animationType="slide"
          transparent={false}
          visible={commentModalVisible}
          onRequestClose={() => setCommentModalVisible(false)}
        >
          <SafeAreaView style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <TouchableOpacity 
                style={styles.backButton}
                onPress={() => setCommentModalVisible(false)}
              >
                <Ionicons name="arrow-back" size={24} color="#fff" />
              </TouchableOpacity>
              <Text style={styles.modalTitle}>Comments</Text>
              <View style={{ width: 24 }} />
            </View>
            
            {selectedPost && (
              <>
                <FlatList
                  data={selectedPost.comments}
                  renderItem={renderComment}
                  keyExtractor={(item) => item.id}
                  contentContainerStyle={styles.commentsList}
                  ListEmptyComponent={
                    <View style={styles.noComments}>
                      <Ionicons name="chatbubble-ellipses-outline" size={48} color="#333" />
                      <Text style={styles.noCommentsText}>No comments yet</Text>
                      <Text style={styles.noCommentsSubtext}>Be the first to comment!</Text>
                    </View>
                  }
                />
                
                <KeyboardAvoidingView 
                  behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                  style={styles.commentInputContainer}
                >
                  <TextInput
                    style={styles.commentInput}
                    placeholder="Write a comment..."
                    placeholderTextColor="#666"
                    value={selectedPost.newComment || ''}
                    onChangeText={(text) => 
                      setSelectedPost({...selectedPost, newComment: text})
                    }
                    multiline
                  />
                  <TouchableOpacity 
                    style={[
                      styles.commentButton,
                      (!selectedPost.newComment?.trim() && styles.commentButtonDisabled)
                    ]}
                    onPress={handleAddComment}
                    disabled={!selectedPost.newComment?.trim()}
                  >
                    <Ionicons 
                      name="send" 
                      size={20} 
                      color={selectedPost.newComment?.trim() ? "#3EC1F9" : "#666"} 
                    />
                  </TouchableOpacity>
                </KeyboardAvoidingView>
              </>
            )}
          </SafeAreaView>
        </Modal>

        {isInitialLoading ? (
          <View style={styles.fullScreenLoaderContainer}>
            <FullScreenLoader />
          </View>
        ) : (
          <React.Fragment>
            {/* Error message */}
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
                <TouchableOpacity onPress={onRefresh} style={styles.retryButton}>
                  <Text style={styles.retryButtonText}>Retry</Text>
                </TouchableOpacity>
              </View>
            )}
            
            {/* Enhanced input container */}
            <Animated.View style={[styles.inputContainer, { height: inputHeight }]}>
              <View style={styles.inputContent}>
                <TextInput
                  ref={inputRef}
                  style={[
                    styles.textInput,
                    isComposing && styles.textInputExpanded
                  ]}
                  placeholder="What is happening?!"
                  placeholderTextColor="#666"
                  value={postText}
                  onChangeText={setPostText}
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                  multiline={isComposing}
                  maxLength={280}
                  editable={!isPosting}
                />
                
                {isComposing && (
                  <View style={styles.composeActions}>
                    <View style={styles.characterCount}>
                      <Text style={[
                        styles.characterCountText,
                        postText.length > 250 && styles.characterCountWarn,
                        postText.length >= 280 && styles.characterCountError
                      ]}>
                        {postText.length}/280
                      </Text>
                    </View>
                    
                    <View style={styles.composeButtons}>
                      <TouchableOpacity 
                        style={styles.cancelButton}
                        onPress={handleCancelCompose}
                        disabled={isPosting}
                      >
                        <Text style={styles.cancelButtonText}>Cancel</Text>
                      </TouchableOpacity>
                      
                      <TouchableOpacity 
                        style={[
                          styles.postButton,
                          (postText.trim() === '' || isPosting) && styles.postButtonDisabled
                        ]}
                        onPress={handlePost}
                        disabled={postText.trim() === '' || isPosting}
                      >
                        {isPosting ? (
                          <LoadingComponent />
                        ) : (
                          <Text style={[
                            styles.postButtonText,
                            (postText.trim() === '' || isPosting) && styles.postButtonTextDisabled
                          ]}>
                            Post
                          </Text>
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
              </View>
            </Animated.View>

            {/* Tabs */}
            <View style={styles.tabsContainer}>
              <TouchableOpacity 
                style={[styles.tab, isTabLoading && { opacity: 0.6 }]}
                onPress={() => handleTabChange('for-you')}
                disabled={isTabLoading}
              >
                <Text style={[styles.tabText, activeTab === 'for-you' && styles.activeTabText]}>
                  For you
                </Text>
                {activeTab === 'for-you' && <View style={styles.tabIndicator} />}
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.tab, isTabLoading && { opacity: 0.6 }]}
                onPress={() => handleTabChange('following')}
                disabled={isTabLoading}
              >
                <Text style={[styles.tabText, activeTab === 'following' && styles.activeTabText]}>
                  Following
                </Text>
                {activeTab === 'following' && <View style={styles.tabIndicator} />}
              </TouchableOpacity>
            </View>

            {/* Feed with optimized loading */}
            <FlatList
              data={posts}
              renderItem={renderPost}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.listContainer}
              refreshControl={
                <RefreshControl 
                  refreshing={isRefreshing} 
                  onRefresh={onRefresh} 
                  tintColor="transparent"
                  colors={['#3EC1F9']}
                />
              }
              showsVerticalScrollIndicator={false}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.5}
              ListHeaderComponent={
                (isRefreshing || isTabLoading) ? (
                  <View style={styles.loadingContainer}>
                    <LoadingComponent />
                  </View>
                ) : null
              }
              ListFooterComponent={
                isLoadingMore ? (
                  <View style={styles.loadingContainer}>
                    <LoadingComponent />
                  </View>
                ) : null
              }
              ListEmptyComponent={
                !isInitialLoading && !isRefreshing && !isTabLoading ? (
                  <View style={styles.emptyContainer}>
                    <Text style={styles.emptyText}>
                      {activeTab === 'following' 
                        ? 'No posts from people you follow yet.' 
                        : 'No posts available.'
                      }
                    </Text>
                  </View>
                ) : null
              }
            />
          </React.Fragment>
        )}
      </SafeAreaView>
    </React.Fragment>
  );
}

const styles = StyleSheet.create({
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'space-between',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#222',
    backgroundColor: '#000',
  },
  backButton: {
    padding: 4,
  },
  backButtonText: {
    color: '#fff',
  },
  modalTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  commentsList: {
    flexGrow: 1,
    paddingBottom: 16,
  },
  noComments: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  noCommentsText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  noCommentsSubtext: {
    color: '#888',
    marginTop: 8,
  },
  commentInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 24,
    borderTopWidth: 1,
    borderTopColor: '#222',
    backgroundColor: '#000',
  },
  commentInput: {
    flex: 1,
    backgroundColor: '#1a1a1a',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    color: '#fff',
    maxHeight: 120,
    marginRight: 8,
    fontSize: 15,
    lineHeight: 20,
  },
  commentButton: {
    padding: 8,
    marginLeft: 4,
  },
  commentButtonDisabled: {
    opacity: 0.5,
  },
  // Comment styles
  commentContainer: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#1a1a1a',
  },
  commentAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
  },
  commentContent: {
    flex: 1,
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  commentAuthor: {
    color: '#fff',
    fontWeight: '600',
    marginRight: 8,
  },
  commentTime: {
    color: '#666',
    fontSize: 12,
  },
  commentText: {
    color: '#fff',
    marginBottom: 8,
  },
  commentActions: {
    flexDirection: 'row',
  },
  commentAction: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  commentActionText: {
    color: '#888',
    fontSize: 12,
    marginLeft: 4,
    minWidth: 16,
    textAlign: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#000',
  },

  // Enhanced input container styles
  inputContainer: {
    paddingHorizontal: 20,
    marginTop: 16,
    marginBottom: 16,
    backgroundColor: '#000',
  },
  inputContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  textInput: {
    fontSize: 18,
    color: '#fff',
    fontWeight: '500',
    paddingVertical: 12,
    paddingHorizontal: 0,
    borderWidth: 0,
    backgroundColor: 'transparent',
  },
  textInputExpanded: {
    flex: 1,
    textAlignVertical: 'top',
    minHeight: 60,
  },
  composeActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  characterCount: {
    flex: 1,
  },
  characterCountText: {
    fontSize: 12,
    color: '#666',
  },
  characterCountWarn: {
    color: '#FF9800',
  },
  characterCountError: {
    color: '#F44336',
  },
  composeButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#333',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  postButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#3EC1F9',
    minWidth: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  postButtonDisabled: {
    backgroundColor: '#333',
    opacity: 0.6,
  },
  postButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  postButtonTextDisabled: {
    color: '#666',
  },
  
  // Enhanced tab styles
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#222',
    marginBottom: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    position: 'relative',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '600',
  },
  activeTabText: {
    color: '#fff',
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: '#3EC1F9',
    borderRadius: 1,
  },
  
  listContainer: {
    paddingBottom: 20,
    flexGrow: 1,
  },
  fullScreenLoaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  errorContainer: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  errorText: {
    color: '#F44336',
    flex: 1,
    marginRight: 12,
  },
  retryButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  retryButtonText: {
    color: '#F44336',
    fontWeight: '600',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    minHeight: 200,
  },
  emptyText: {
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
  
  // Post styles
  postContainer: {
    backgroundColor: '#000',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#111',
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
  },
  userNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  authorName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  handle: {
    color: '#888',
    fontSize: 14,
  },
  performance: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  date: {
    color: '#888',
    fontSize: 14,
    marginTop: 2,
  },
  postTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  postContent: {
    color: '#fff',
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 16,
  },
  reactionsContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 16,
  },
  reactionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#111',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  reactionActive: {
    backgroundColor: '#222',
  },
  reactionEmoji: {
    fontSize: 16,
  },
  reactionCount: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  
  // Enhanced post actions - full width
  postActions: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 0,
  },
  voteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    flex: 1,
    justifyContent: 'flex-start',
  },
  voteText: {
    color: '#888',
    fontSize: 14,
    fontWeight: '600',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 16,
    padding: 4,
  },
  actionText: {
    marginLeft: 4,
    
    color: '#888',
    fontSize: 14,
    minWidth: 20,
    textAlign: 'center',
  },
});