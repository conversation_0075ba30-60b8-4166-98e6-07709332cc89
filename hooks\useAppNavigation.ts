import { useRouter } from 'expo-router';
import { Platform } from 'react-native';

type AppRoute = 
  | 'home'
  | 'wallet'
  | 'community'
  | 'rankings'
  | 'profile'
  | 'messenger'
  | { name: 'chat', params: { 
      userId: string; 
      name: string; 
      avatar: string; 
      handle: string; 
      online: string 
    }}
  | 'selectFriend'
  | 'messenger/select-friend';

// Type guard to check if the route is a chat route
const isChatRoute = (route: AppRoute): route is Extract<AppRoute, { name: 'chat' }> => {
  return typeof route === 'object' && 'name' in route && route.name === 'chat';
};

// Type assertion to handle expo-router's strict route types
const asRoute = (path: string) => path as any;

const useAppNavigation = () => {
  const router = useRouter();

  const navigate = (route: AppRoute) => {
    if (isChatRoute(route)) {
      // Handle chat route with params
      const { userId, name, avatar, handle, online } = route.params;
      router.push({
        pathname: asRoute('/chat'),
        params: { userId, name, avatar, handle, online }
      });
      return;
    }

    // Handle simple routes
    switch (route) {
      case 'home':
        router.replace(asRoute('/(tabs)'));
        break;
      case 'wallet':
        router.replace(asRoute('/(tabs)/wallet'));
        break;
      case 'community':
        router.replace(asRoute('/(tabs)/community'));
        break;
      case 'rankings':
        router.replace(asRoute('/(tabs)/rankings'));
        break;
      case 'profile':
        router.replace(asRoute('/(tabs)/profile'));
        break;
      case 'messenger':
        if (Platform.OS === 'web') {
          router.replace(asRoute('/messenger'));
        } else {
          router.replace(asRoute('/messenger'));
        }
        break;
      case 'selectFriend':
        if (Platform.OS === 'web') {
          router.push(asRoute('/select-friend'));
        } else {
          router.push({
            pathname: asRoute('/(tabs)/select-friend'),
            params: {}
          });
        }
        break;
      case 'messenger/select-friend':
        if (Platform.OS === 'web') {
          router.push(asRoute('/messenger/select-friend'));
        } else {
          router.push({
            pathname: asRoute('/messenger/select-friend'),
            params: {}
          });
        }
        break;
    }
  };

  return { navigate };
};

export default useAppNavigation;
