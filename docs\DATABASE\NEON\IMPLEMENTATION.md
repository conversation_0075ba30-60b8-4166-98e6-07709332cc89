# Neon Database Implementation

## Overview
This document outlines the Neon-based database architecture for the HodlHub Communities application, leveraging serverless PostgreSQL with autoscaling and branching capabilities.

## Architecture Components

### 1. Database Schema

Neon uses standard PostgreSQL, so we'll use a similar schema to the Supabase implementation but with some optimizations for serverless environments.

```sql
-- Enable necessary extensions
create extension if not exists "uuid-ossp";
create extension if not exists "pgcrypto";
create extension if not exists "pgjwt";

-- Custom types
create type post_visibility as enum ('public', 'followers', 'private');

-- Tables
create table public.users (
  id uuid primary key default gen_random_uuid(),
  username text not null unique,
  email text unique,
  full_name text,
  avatar_url text,
  portfolio_change numeric(5,2),
  is_online boolean default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint username_format check (username ~* '^[a-zA-Z0-9_]+$'),
  constraint username_length check (char_length(username) between 3 and 30)
);

-- Optimized for read performance with partitioning
create table public.posts (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references public.users(id) on delete cascade,
  content text not null,
  title text,
  image_url text,
  performance_indicator text,
  visibility post_visibility not null default 'public',
  upvotes bigint not null default 0,
  downvotes bigint not null default 0,
  comment_count bigint not null default 0,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  -- For time-series partitioning
  post_date date not null default now()::date
) partition by range (post_date);

-- Create monthly partitions for posts
create table public.posts_y2024m06 partition of public.posts
  for values from ('2024-06-01') to ('2024-07-01');

-- Indexes for posts
create index idx_posts_user_id on public.posts(user_id);
create index idx_posts_created_at on public.posts(created_at desc);
create index idx_posts_visibility on public.posts(visibility, created_at desc);

-- Comments with partitioning by post_id for co-location
create table public.comments (
  id uuid primary key default gen_random_uuid(),
  post_id uuid not null,
  user_id uuid not null references public.users(id) on delete cascade,
  content text not null,
  like_count bigint not null default 0,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint fk_post foreign key (post_id) references public.posts(id) on delete cascade
) partition by hash (post_id);

-- Create 4 hash partitions for comments
create table public.comments_p0 partition of public.comments
  for values with (modulus 4, remainder 0);
create table public.comments_p1 partition of public.comments
  for values with (modulus 4, remainder 1);
create table public.comments_p2 partition of public.comments
  for values with (modulus 4, remainder 2);
create table public.comments_p3 partition of public.comments
  for values with (modulus 4, remainder 3);

-- Optimized reactions table with composite key
create table public.reactions (
  user_id uuid not null references public.users(id) on delete cascade,
  entity_type text not null check (entity_type in ('post', 'comment')),
  entity_id uuid not null,
  type text not null,
  created_at timestamptz not null default now(),
  primary key (user_id, entity_type, entity_id, type)
) partition by hash (user_id);

-- Create 4 hash partitions for reactions
create table public.reactions_p0 partition of public.reactions
  for values with (modulus 4, remainder 0);
create table public.reactions_p1 partition of public.reactions
  for values with (modulus 4, remainder 1);
create table public.reactions_p2 partition of public.reactions
  for values with (modulus 4, remainder 2);
create table public.reactions_p3 partition of public.reactions
  for values with (modulus 4, remainder 3);

-- User relationships with optimized for social graph queries
create table public.user_relationships (
  follower_id uuid not null references public.users(id) on delete cascade,
  following_id uuid not null references public.users(id) on delete cascade,
  created_at timestamptz not null default now(),
  primary key (follower_id, following_id),
  constraint no_self_follow check (follower_id != following_id)
);

-- Optimized index for social graph queries
create index idx_user_relationships_following on public.user_relationships(following_id);

-- Messages with partitioning by conversation_id
create table public.conversations (
  id uuid primary key default gen_random_uuid(),
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

create table public.conversation_participants (
  conversation_id uuid not null references public.conversations(id) on delete cascade,
  user_id uuid not null references public.users(id) on delete cascade,
  last_read_at timestamptz,
  primary key (conversation_id, user_id)
);

-- Partitioned messages for better performance
create table public.messages (
  id uuid primary key default gen_random_uuid(),
  conversation_id uuid not null,
  sender_id uuid not null references public.users(id) on delete cascade,
  content text not null,
  created_at timestamptz not null default now(),
  constraint fk_conversation foreign key (conversation_id) 
    references public.conversations(id) on delete cascade
) partition by hash (conversation_id);

-- Create 4 hash partitions for messages
create table public.messages_p0 partition of public.messages
  for values with (modulus 4, remainder 0);
create table public.messages_p1 partition of public.messages
  for values with (modulus 4, remainder 1);
create table public.messages_p2 partition of public.messages
  for values with (modulus 4, remainder 2);
create table public.messages_p3 partition of public.messages
  for values with (modulus 4, remainder 3);

-- Indexes for messages
create index idx_messages_conversation_created on public.messages(conversation_id, created_at);
create index idx_messages_sender on public.messages(sender_id);

-- Materialized views for analytics
create materialized view public.user_engagement_metrics as
select 
  user_id,
  count(distinct p.id) as post_count,
  count(distinct c.id) as comment_count,
  sum(p.upvotes) as total_upvotes_received,
  count(distinct r.follower_id) as follower_count,
  max(p.created_at) as last_post_at
from public.users u
left join public.posts p on p.user_id = u.id
left join public.comments c on c.user_id = u.id
left join public.user_relationships r on r.following_id = u.id
group by u.id;

-- Update function for materialized view
create or replace function refresh_user_engagement_metrics()
returns trigger
language plpgsql
as $$
begin
  refresh materialized view concurrently public.user_engagement_metrics;
  return null;
end;
$$;

-- Trigger for materialized view refresh
create trigger refresh_user_engagement_after_post
after insert or update or delete on public.posts
for each statement execute function refresh_user_engagement_metrics();

-- Similar triggers for other tables...
```

### 2. Row Level Security (RLS)

```sql
-- Enable RLS on all tables
alter table public.users enable row level security;
alter table public.posts enable row level security;
alter table public.comments enable row level security;
alter table public.reactions enable row level security;
alter table public.user_relationships enable row level security;
alter table public.conversations enable row level security;
alter table public.conversation_participants enable row level security;
alter table public.messages enable row level security;

-- Users policies
create policy "Users can view all users"
  on public.users for select
  using (true);

create policy "Users can update own profile"
  on public.users for update
  using (auth.uid() = id);

-- Posts policies
create policy "Public posts are viewable by everyone"
  on public.posts for select
  using (visibility = 'public');

create policy "Users can view their own posts"
  on public.posts for select
  using (user_id = auth.uid());

create policy "Users can view followers-only posts from users they follow"
  on public.posts for select
  using (
    visibility = 'followers' and
    exists (
      select 1 from public.user_relationships
      where follower_id = auth.uid()
      and following_id = user_id
    )
  );

create policy "Users can create posts"
  on public.posts for insert
  with check (user_id = auth.uid());

-- Similar policies for other tables...
```

### 3. Database Functions

```sql
-- Function to get user feed with pagination
create or replace function public.get_user_feed(
  p_user_id uuid,
  p_limit int default 10,
  p_offset int default 0
)
returns table (
  id uuid,
  user_id uuid,
  username text,
  avatar_url text,
  content text,
  title text,
  image_url text,
  performance_indicator text,
  upvotes bigint,
  downvotes bigint,
  comment_count bigint,
  created_at timestamptz,
  is_liked boolean,
  is_owner boolean
)
language sql
security definer
as $$
  select 
    p.*,
    u.username,
    u.avatar_url,
    exists (
      select 1 from public.reactions r 
      where r.entity_type = 'post' 
      and r.entity_id = p.id 
      and r.user_id = p_user_id
      and r.type = 'like'
    ) as is_liked,
    (p.user_id = p_user_id) as is_owner
  from public.posts p
  join public.users u on p.user_id = u.id
  where p.visibility = 'public'
     or (p.visibility = 'followers' and exists (
       select 1 from public.user_relationships ur 
       where ur.follower_id = p_user_id 
       and ur.following_id = p.user_id
     ))
     or p.user_id = p_user_id
  order by p.created_at desc
  limit p_limit
  offset p_offset;
$$;

-- Function to create a post with transaction
create or replace function public.create_post(
  p_user_id uuid,
  p_content text,
  p_title text default null,
  p_image_url text default null,
  p_performance_indicator text default null,
  p_visibility post_visibility default 'public'
)
returns uuid
language plpgsql
security definer
as $$
declare
  v_post_id uuid;
begin
  -- Insert the post
  insert into public.posts (
    user_id, 
    content, 
    title, 
    image_url, 
    performance_indicator, 
    visibility,
    post_date
  )
  values (
    p_user_id,
    p_content,
    p_title,
    p_image_url,
    p_performance_indicator,
    p_visibility,
    now()::date
  )
  returning id into v_post_id;
  
  -- Update user's post count (denormalized)
  update public.users
  set post_count = post_count + 1
  where id = p_user_id;
  
  return v_post_id;
end;
$$;

-- Function to handle reactions with upsert
create or replace function public.handle_reaction(
  p_user_id uuid,
  p_entity_type text,
  p_entity_id uuid,
  p_reaction_type text,
  p_action text -- 'add' or 'remove'
)
returns void
language plpgsql
security definer
as $$
begin
  if p_action = 'add' then
    insert into public.reactions (user_id, entity_type, entity_id, type)
    values (p_user_id, p_entity_type, p_entity_id, p_reaction_type)
    on conflict (user_id, entity_type, entity_id, type) do nothing;
    
    -- Update denormalized counts
    if p_entity_type = 'post' then
      update public.posts
      set upvotes = case 
        when p_reaction_type = 'upvote' then upvotes + 1
        when p_reaction_type = 'downvote' then upvotes - 1
        else upvotes
      end
      where id = p_entity_id;
    end if;
  else
    delete from public.reactions
    where user_id = p_user_id
    and entity_type = p_entity_type
    and entity_id = p_entity_id
    and type = p_reaction_type;
    
    -- Update denormalized counts
    if p_entity_type = 'post' then
      update public.posts
      set upvotes = case 
        when p_reaction_type = 'upvote' then upvotes - 1
        when p_reaction_type = 'downvote' then upvotes + 1
        else upvotes
      end
      where id = p_entity_id;
    end if;
  end if;
end;
$$;
```

## Data Access Layer

### 1. Client Implementation with Connection Pooling

```typescript
// lib/neon.ts
import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import * as schema from './schema';

// Connection pool configuration
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 10, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
  maxUses: 7500, // Close and replace a connection after it has been used 7500 times
});

// Initialize Drizzle ORM
export const db = drizzle(pool, { schema });

// Example: Get user feed
export async function getUserFeed(userId: string, page = 0, pageSize = 10) {
  const offset = page * pageSize;
  const query = `
    SELECT * FROM get_user_feed($1, $2, $3)
  `;
  
  const client = await pool.connect();
  try {
    const result = await client.query(query, [userId, pageSize, offset]);
    return result.rows;
  } catch (error) {
    console.error('Error fetching user feed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Example: Create a post
export async function createPost({
  userId,
  content,
  title,
  imageUrl,
  performanceIndicator,
  visibility = 'public',
}: {
  userId: string;
  content: string;
  title?: string;
  imageUrl?: string;
  performanceIndicator?: string;
  visibility?: 'public' | 'followers' | 'private';
}) {
  const query = `
    SELECT create_post($1, $2, $3, $4, $5, $6) as post_id;
  `;
  
  const client = await pool.connect();
  try {
    const result = await client.query(query, [
      userId,
      content,
      title,
      imageUrl,
      performanceIndicator,
      visibility,
    ]);
    
    return { id: result.rows[0].post_id };
  } catch (error) {
    console.error('Error creating post:', error);
    throw error;
  } finally {
    client.release();
  }
}
```

### 2. Real-time Updates with WebSockets

```typescript
// lib/realtime.ts
import { WebSocket } from 'ws';
import { v4 as uuidv4 } from 'uuid';
import { pool } from './neon';

interface Subscription {
  id: string;
  userId: string;
  channel: string;
  callback: (payload: any) => void;
  ws?: WebSocket;
}

class RealtimeClient {
  private subscriptions: Map<string, Subscription> = new Map();
  private pgClient: any = null;
  
  constructor() {
    this.setupDatabaseListeners();
  }
  
  private async setupDatabaseListeners() {
    // Use a dedicated connection for LISTEN/NOTIFY
    this.pgClient = (await pool.connect()).release();
    
    // Listen for new posts
    await this.pgClient.query('LISTEN new_post');
    await this.pgClient.query('LISTEN new_comment');
    await this.pgClient.query('LISTEN new_message');
    
    // Handle notifications
    this.pgClient.on('notification', (msg: any) => {
      try {
        const payload = JSON.parse(msg.payload);
        this.handleNotification(msg.channel, payload);
      } catch (error) {
        console.error('Error handling notification:', error);
      }
    });
  }
  
  private handleNotification(channel: string, payload: any) {
    // Find all subscriptions for this channel
    for (const [id, sub] of this.subscriptions.entries()) {
      if (sub.channel === channel) {
        try {
          sub.callback(payload);
        } catch (error) {
          console.error(`Error in subscription ${id} callback:`, error);
        }
      }
    }
  }
  
  public subscribe(
    userId: string,
    channel: string,
    callback: (payload: any) => void,
    ws?: WebSocket
  ): string {
    const id = uuidv4();
    this.subscriptions.set(id, { id, userId, channel, callback, ws });
    
    // Setup WebSocket close handler
    if (ws) {
      ws.on('close', () => this.unsubscribe(id));
    }
    
    return id;
  }
  
  public unsubscribe(subscriptionId: string): boolean {
    return this.subscriptions.delete(subscriptionId);
  }
  
  // Notify all subscribers of a new post
  public async notifyNewPost(post: any) {
    const query = `
      SELECT pg_notify('new_post', $1);
    `;
    
    const client = await pool.connect();
    try {
      await client.query(query, [JSON.stringify({
        event: 'new_post',
        data: post,
        timestamp: new Date().toISOString()
      })]);
    } catch (error) {
      console.error('Error sending notification:', error);
    } finally {
      client.release();
    }
  }
  
  // Similar methods for comments, messages, etc.
}

export const realtime = new RealtimeClient();

// Trigger in your API route when a new post is created
// await realtime.notifyNewPost(newPost);
```

## Authentication

For authentication, we'll use JWT with a custom auth service:

```typescript
// lib/auth.ts
import { sign, verify, decode, JwtPayload } from 'jsonwebtoken';
import { db } from './neon';
import { users } from './schema';
import { eq } from 'drizzle-orm';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = '7d';

export async function createAuthToken(userId: string) {
  const token = sign({ sub: userId }, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
  });
  
  // Store refresh token in database
  const refreshToken = sign({}, JWT_SECRET, { expiresIn: '30d' });
  
  await db.update(users)
    .set({ refreshToken })
    .where(eq(users.id, userId));
    
  return { token, refreshToken };
}

export async function verifyToken(token: string) {
  try {
    const payload = verify(token, JWT_SECRET) as JwtPayload;
    return payload.sub as string;
  } catch (error) {
    return null;
  }
}

export async function refreshAuthToken(refreshToken: string) {
  // Verify refresh token from database
  const [user] = await db.select()
    .from(users)
    .where(eq(users.refreshToken, refreshToken))
    .limit(1);
    
  if (!user) {
    throw new Error('Invalid refresh token');
  }
  
  // Generate new tokens
  return createAuthToken(user.id);
}
```

## Performance Optimization

1. **Connection Pooling**:
   - Configured connection pooling with appropriate limits
   - Connection reuse for better performance

2. **Query Optimization**:
   - Used prepared statements
   - Implemented proper indexing
   - Used partitioning for large tables

3. **Caching Layer**:
   - Implemented with Redis or Upstash
   - Cache frequently accessed data
   - Invalidation strategies

4. **Read Replicas**:
   - Neon's read replicas for read scaling
   - Automatic failover

## Security

1. **Authentication**:
   - JWT with short expiration
   - Secure token storage
   - Refresh token rotation

2. **Database Security**:
   - SSL/TLS for all connections
   - Row Level Security (RLS)
   - Parameterized queries

3. **API Security**:
   - Rate limiting
   - CORS configuration
   - Input validation

## Deployment

1. **Migrations**:
   ```bash
   # Install migrations tool
   npm install -g db-migrate
   
   # Create new migration
   db-migrate create add-posts-table --sql-file
   
   # Run migrations
   db-migrate up
   ```

2. **Environment Variables**:
   ```env
   DATABASE_URL=postgres://user:<EMAIL>/neondb
   JWT_SECRET=your-secret-key
   NODE_ENV=production
   ```

3. **CI/CD**:
   - GitHub Actions for CI/CD
   - Automated testing
   - Zero-downtime deployments

## Monitoring

1. **Neon Dashboard**:
   - Query performance
   - Connection metrics
   - Storage usage

2. **Application Logs**:
   - Structured logging
   - Error tracking
   - Performance monitoring

3. **Alerts**:
   - Anomaly detection
   - Error rate alerts
   - Performance degradation

## Cost Estimation

1. **Free Tier**:
   - 3 projects
   - 500MB storage
   - 10K rows/hour
   - No credit card required

2. **Paid Plans**:
   - Pro: $20/month
     - 8GB storage
     - 1M rows/hour
     - 100GB transfer
   
   - Team: Custom pricing
     - Custom limits
     - Priority support
     - Dedicated instances

## Next Steps

1. Set up local development environment
2. Configure CI/CD pipeline
3. Implement monitoring and alerting
4. Plan for scaling
5. Set up backup strategy

---
*Document Version: 1.0*
*Last Updated: 2024-06-14*
