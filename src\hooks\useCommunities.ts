import { useState, useEffect, useCallback } from 'react';
import { Community, UseCommunitiesReturn, BASE_COMMUNITIES } from '@src/types/community';

export function useCommunities(): UseCommunitiesReturn {
  const [communities, setCommunities] = useState<Community[]>([]);
  const [filteredCommunities, setFilteredCommunities] = useState<Community[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Initialize communities
  useEffect(() => {
    const initializeCommunities = async () => {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCommunities(BASE_COMMUNITIES);
      setFilteredCommunities(BASE_COMMUNITIES);
      setIsLoading(false);
    };

    initializeCommunities();
  }, []);

  // Filter communities based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredCommunities(communities);
    } else {
      const filtered = communities.filter(community =>
        community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        community.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCommunities(filtered);
    }
  }, [communities, searchQuery]);

  const handleSearch = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    // Simulate API refresh
    await new Promise(resolve => setTimeout(resolve, 1500));
    setCommunities([...BASE_COMMUNITIES]);
    setIsRefreshing(false);
  }, []);

  const loadMoreCommunities = useCallback(async () => {
    if (isLoadingMore || searchQuery.trim()) return;
    
    setIsLoadingMore(true);
    // Simulate loading more data
    await new Promise(resolve => setTimeout(resolve, 1000));
    // In a real app, you would fetch more data here
    setIsLoadingMore(false);
  }, [isLoadingMore, searchQuery]);

  const handleJoinToggle = useCallback((id: string, isJoining: boolean) => {
    setCommunities(prev => 
      prev.map(community => 
        community.id === id 
          ? { ...community, joined: isJoining }
          : community
      )
    );
  }, []);

  return {
    communities,
    filteredCommunities,
    searchQuery,
    isLoading,
    isRefreshing,
    isLoadingMore,
    handleSearch,
    handleRefresh,
    loadMoreCommunities,
    handleJoinToggle,
  };
}
