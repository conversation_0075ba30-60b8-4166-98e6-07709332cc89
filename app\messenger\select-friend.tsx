import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, FlatList, Keyboard, TouchableWithoutFeedback, SafeAreaView, ActivityIndicator } from 'react-native';
import useAppNavigation from '../../hooks/useAppNavigation';
import { Ionicons } from '@expo/vector-icons';
import { mockFriends } from '../../types/messaging';
import FriendListItem from '../../components/ui/FriendListItem';
import { Friend } from '../../types/messaging';

const SelectFriendScreen = () => {
  const { navigate } = useAppNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredFriends, setFilteredFriends] = useState<Friend[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Initialize with all friends
  useEffect(() => {
    setFilteredFriends(mockFriends);
  }, []);

  // Debounce search
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredFriends(mockFriends);
      return;
    }

    const searchDelay = setTimeout(() => {
      const query = searchQuery.toLowerCase();
      const filtered = mockFriends.filter(
        friend =>
          friend.name.toLowerCase().includes(query) ||
          friend.handle.toLowerCase().includes(query)
      );
      setFilteredFriends(filtered);
      setIsSearching(false);
    }, 300);

    return () => clearTimeout(searchDelay);
  }, [searchQuery]);

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    if (text.trim() !== '') {
      setIsSearching(true);
    }
  };

  const handleSelectFriend = useCallback((friend: Friend) => {
    navigate({
      name: 'chat',
      params: {
        userId: friend.id,
        name: friend.name,
        avatar: friend.avatar,
        handle: friend.handle,
        online: String(friend.online)
      }
    });
  }, [navigate]);

  const handleClearSearch = () => {
    setSearchQuery('');
    setFilteredFriends(mockFriends);
  };

  const handleBack = () => {
    navigate('messenger');
  };

  const renderFriend = ({ item }: { item: Friend }) => (
    <FriendListItem friend={item} onPress={() => handleSelectFriend(item)} />
  );

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableWithoutFeedback 
            onPress={handleBack}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableWithoutFeedback>
          <Text style={styles.headerTitle}>New Message</Text>
          <View style={{ width: 24 }} />
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search friends"
              placeholderTextColor="#666"
              value={searchQuery}
              onChangeText={handleSearch}
              autoCapitalize="none"
              autoCorrect={false}
              clearButtonMode="while-editing"
              returnKeyType="search"
              onSubmitEditing={() => Keyboard.dismiss()}
            />
            {searchQuery.length > 0 && (
              <TouchableWithoutFeedback onPress={handleClearSearch}>
                <Ionicons name="close-circle" size={18} color="#666" style={styles.clearIcon} />
              </TouchableWithoutFeedback>
            )}
          </View>
        </View>

        {/* Loading Indicator */}
        {isSearching && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#3EC1F9" />
          </View>
        )}

        {/* Friends List */}
        <FlatList
          data={filteredFriends}
          renderItem={renderFriend}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          keyboardShouldPersistTaps="handled"
          ListEmptyComponent={
            !isSearching && searchQuery ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>No friends found</Text>
                <Text style={styles.emptySubtext}>Try a different search term</Text>
              </View>
            ) : null
          }
        />
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#1A1A1A',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1A1A1A',
    borderRadius: 10,
    paddingHorizontal: 12,
    height: 44,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    color: '#fff',
    fontSize: 16,
    height: '100%',
    paddingVertical: 0,
  },
  clearIcon: {
    marginLeft: 8,
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'center',
  },
  listContent: {
    paddingBottom: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 100,
  },
  emptyText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptySubtext: {
    color: '#666',
    fontSize: 14,
  },
});

export default SelectFriendScreen;
