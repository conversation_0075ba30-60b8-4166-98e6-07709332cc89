import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, ImageSourcePropType } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Post } from '../types/post';

interface PostCardProps {
  item: Post;
}

const PostCard: React.FC<PostCardProps> = React.memo(({ item }) => {
  const renderAvatar = () => {
    if (typeof item.user.avatar === 'string') {
      return (
        <Image 
          source={{ uri: item.user.avatar }} 
          style={styles.userAvatar} 
        />
      );
    }
    return <Image source={item.user.avatar} style={styles.userAvatar} />;
  };

  return (
    <View style={styles.postContainer}>
      <View style={styles.postHeader}>
        {renderAvatar()}
        <View style={styles.postHeaderInfo}>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{item.user.name}</Text>
            <Text style={styles.userHandle}>{item.user.handle}</Text>
          </View>
          <Text style={styles.timestamp}>{item.timestamp}</Text>
        </View>
        {item.performanceIndicator && (
          <Text 
            style={[
              styles.performanceIndicator, 
              { color: item.performanceIndicator.startsWith('+') ? '#4CAF50' : '#F44336' }
            ]}
          >
            {item.performanceIndicator}
          </Text>
        )}
      </View>
      
      <Text style={styles.postContent}>{item.content}</Text>
      
      {item.image && (
        <Image 
          source={{ uri: item.image }} 
          style={styles.postImage} 
          resizeMode="cover"
        />
      )}
      
      <View style={styles.postActions}>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="chatbubble-outline" size={20} color="#666" />
          <Text style={styles.actionText}>{item.stats.comments}</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="repeat-outline" size={20} color="#666" />
          <Text style={styles.actionText}>{item.stats.reposts}</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="heart-outline" size={20} color="#666" />
          <Text style={styles.actionText}>{item.stats.likes}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  postContainer: {
    backgroundColor: '#121212',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  postHeaderInfo: {
    flex: 1,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  userName: {
    color: '#fff',
    fontWeight: 'bold',
    marginRight: 8,
  },
  userHandle: {
    color: '#666',
    fontSize: 14,
  },
  timestamp: {
    color: '#666',
    fontSize: 12,
  },
  performanceIndicator: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  postContent: {
    color: '#fff',
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 12,
  },
  postImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 12,
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#222',
    paddingTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionText: {
    color: '#666',
    marginLeft: 6,
    fontSize: 14,
  },
});

export default PostCard;
