import React from 'react';
import { Text as RNText, TextProps as RNTextProps, StyleSheet } from 'react-native';

type TextVariant = 'h1' | 'h2' | 'h3' | 'h4' | 'body' | 'body2' | 'caption' | 'button' | 'label';

type TextWeight = 'regular' | 'medium' | 'semiBold' | 'bold';

interface TextProps extends RNTextProps {
  variant?: TextVariant;
  weight?: TextWeight;
  children: React.ReactNode;
}

const fontWeights = {
  regular: 'Inter_400Regular',
  medium: 'Inter_500Medium',
  semiBold: 'Inter_600SemiBold',
  bold: 'Inter_700Bold',
};

const variantStyles = StyleSheet.create({
  h1: {
    fontSize: 32,
    lineHeight: 40,
  },
  h2: {
    fontSize: 28,
    lineHeight: 36,
  },
  h3: {
    fontSize: 24,
    lineHeight: 32,
  },
  h4: {
    fontSize: 20,
    lineHeight: 28,
  },
  body: {
    fontSize: 16,
    lineHeight: 24,
  },
  body2: {
    fontSize: 14,
    lineHeight: 20,
  },
  caption: {
    fontSize: 12,
    lineHeight: 16,
  },
  button: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
  },
  label: {
    fontSize: 14,
    lineHeight: 20,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
});

export const Text: React.FC<TextProps> = ({
  variant = 'body',
  weight = 'regular',
  style,
  children,
  ...props
}) => {
  return (
    <RNText
      style={[
        {
          fontFamily: fontWeights[weight],
          color: 'white', // This will be overridden by theme
        },
        variantStyles[variant],
        style,
      ]}
      {...props}
    >
      {children}
    </RNText>
  );
};

export default Text;
