Enhance the community mock data in the HodlHub app with the following requirements:

1. Community Data Structure:
   - Generate 15-20 unique community objects with realistic names related to crypto, DeFi, NFTs, and blockchain
   - Each community should have:
     - id: Unique identifier (UUID or incremental number)
     - name: Creative and engaging name (e.g., "<PERSON><PERSON><PERSON>", "NFT Art Collective")
     - handle: @community_handle (lowercase, no spaces)
     - avatar: URL to a relevant avatar/image
     - banner: URL to a banner image
     - description: 1-2 sentences about the community's purpose
     - memberCount: Random number between 100-50,000
     - isVerified: Boolean (some communities should be verified)
     - tags: Array of 2-4 relevant tags (e.g., ["DeFi", "Trading", "Ethereum"])
     - createdAt: Random date within the last 2 years
     - isMember: Boolean (randomly set)

2. Community Feed Data:
   - Generate 5-10 sample posts for each community
   - Each post should have:
     - id: Unique identifier
     - author: Reference to a user
     - content: 1-3 sentences of engaging content
     - image: Optional image URL (some posts should have images)
     - likes: Random number (0-1000)
     - comments: Random number (0-500)
     - reposts: Random number (0-200)
     - timestamp: Recent date (within last 7 days)
     - isLiked: Boolean
     - isReposted: Boolean

3. User Data:
   - Create 10-15 unique user profiles
   - Each user should have:
     - id: Unique identifier
     - name: Full name
     - handle: @username
     - avatar: URL to profile picture
     - bio: Short bio
     - isVerified: Boolean (some users should be verified)
     - followerCount: Random number
     - followingCount: Random number

4. Mock Data Requirements:
   - Use realistic crypto/blockchain terminology
   - Include variety in post types: text, image, and link posts
   - Ensure consistent data structure throughout
   - Add some trending topics/hashtags
   - Include some engagement metrics (likes, comments, etc.)
   - Make sure all image URLs are valid and point to actual images

5. Implementation:
   - Create a new file called `mockCommunityData.ts` in the community data directory
   - Export the mock data as named exports
   - Include proper TypeScript types/interfaces
   - Add JSDoc comments for better code documentation

6. Style Guidelines:
   - Follow existing code style and formatting
   - Use consistent naming conventions
   - Keep the data realistic and engaging
   - Ensure all strings are properly escaped and formatted

The mock data should be ready to use with the existing community components and provide a realistic representation of an active crypto community.