import React from 'react';
import { View, Text } from 'react-native';

export default function NativeWindTest() {
  return (
    <View className="flex-1 justify-center items-center bg-background-primary">
      <Text className="text-text-primary text-2xl font-bold mb-4">
        🎉 NativeWind is Working!
      </Text>
      <View className="bg-primary p-4 rounded-lg">
        <Text className="text-white text-center">
          This is styled with TailwindCSS classes
        </Text>
      </View>
      <View className="mt-4 flex-row space-x-2">
        <View className="bg-success p-2 rounded">
          <Text className="text-white text-sm">Success</Text>
        </View>
        <View className="bg-warning p-2 rounded">
          <Text className="text-white text-sm">Warning</Text>
        </View>
        <View className="bg-error p-2 rounded">
          <Text className="text-white text-sm">Error</Text>
        </View>
      </View>
    </View>
  );
}
