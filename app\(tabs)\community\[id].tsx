import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import {
  SafeAreaView,
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  ScrollView,
  Share,
  Animated,
  Alert,
  Platform,
  ActivityIndicator,
  TextInput,
  Dimensions,
  ImageBackground
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useLoadingStates } from '../../../hooks/useLoadingStates';
import LoadingWhale from '../../../components/ui/LoadingWhale';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Type definitions (enhanced)
interface Reaction {
  emoji: string;
  count: number;
  isActive: boolean;
  color?: string;
}

interface Comment {
  id: string;
  author: string;
  avatar: string;
  content: string;
  timestamp: string;
  likes: number;
  isLiked: boolean;
  isModerator?: boolean;
  replies?: Comment[];
}

interface CommunityPost {
  id: string;
  author: string;
  handle: string;
  avatar: string;
  performance?: string;
  performanceType?: 'positive' | 'negative';
  date: string;
  title: string;
  content: string;
  reactions: Reaction[];
  upvotes: number;
  downvotes: number;
  comments: Comment[];
  isUpvoted: boolean;
  isDownvoted: boolean;
  showComments?: boolean;
  newComment?: string;
  isPinned?: boolean;
  memberOnly?: boolean;
  moderatorPost?: boolean;
  tags?: string[];
  media?: { type: 'image' | 'video'; url: string }[];
}

interface CommunityDetail {
  id: string;
  name: string;
  description: string;
  members: number;
  posts: number;
  image: string;
  coverImage?: string;
  joined: boolean;
  rules: string[];
  moderators: { name: string; avatar: string; role: string }[];
  created: string;
  category: string;
  isPrivate: boolean;
  isPremium: boolean;
  price?: number;
  membershipTier?: 'free' | 'premium' | 'vip';
  onlineMembers?: number;
  badges?: string[];
  trending?: boolean;
}

// Enhanced mock data generator with memoization
const useCommunityDetail = (id: string): CommunityDetail => {
  return useMemo(() => {
    const communities: Record<string, Partial<CommunityDetail>> = {
      'bitcoin-bulls': {
        name: 'Bitcoin Bulls',
        description: 'The premier community for long-term Bitcoin investors and HODLers. Join thousands of members discussing market trends, investment strategies, and the future of digital gold.',
        members: 2547,
        posts: 1823,
        image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
        coverImage: 'https://images.unsplash.com/photo-1516245834210-c4c142787335?w=800',
        category: 'Cryptocurrency',
        badges: ['Verified', 'Top Community'],
        trending: true,
        onlineMembers: 342,
        membershipTier: 'premium',
        moderators: [
          { name: 'Alex Thompson', avatar: 'https://i.pravatar.cc/150?img=1', role: 'Founder' },
          { name: 'Sarah Chen', avatar: 'https://i.pravatar.cc/150?img=2', role: 'Lead Moderator' },
          { name: 'Mike Rodriguez', avatar: 'https://i.pravatar.cc/150?img=3', role: 'Community Manager' }
        ]
      },
      'defi-pioneers': {
        name: 'DeFi Pioneers',
        description: 'Exploring the future of decentralized finance. Join us to discuss yield farming, liquidity pools, and the latest DeFi protocols shaping the financial landscape.',
        members: 1832,
        posts: 945,
        image: 'https://assets.coingecko.com/coins/images/12504/large/uni.jpg',
        coverImage: 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=800',
        category: 'DeFi',
        badges: ['Verified'],
        onlineMembers: 156,
        membershipTier: 'free',
        moderators: [
          { name: 'Emma Wilson', avatar: 'https://i.pravatar.cc/150?img=4', role: 'DeFi Expert' },
          { name: 'James Liu', avatar: 'https://i.pravatar.cc/150?img=5', role: 'Technical Lead' }
        ]
      },
      'nft-collectors': {
        name: 'NFT Collectors',
        description: 'The ultimate digital art and NFT trading community. Share your collections, discover emerging artists, and stay updated on the latest drops and market trends.',
        members: 3211,
        posts: 2156,
        image: 'https://assets.coingecko.com/coins/images/13446/large/5f6294c0c7a8cda55cb1c936_Flow_Wordmark.png',
        coverImage: 'https://images.unsplash.com/photo-1646463910506-e43e17e26e58?w=800',
        category: 'NFTs',
        isPremium: true,
        price: 9.99,
        badges: ['Premium', 'Artist Verified'],
        trending: true,
        onlineMembers: 567,
        membershipTier: 'vip',
        moderators: [
          { name: 'ArtCrypto', avatar: 'https://i.pravatar.cc/150?img=6', role: 'Art Curator' },
          { name: 'PixelMaster', avatar: 'https://i.pravatar.cc/150?img=7', role: 'NFT Expert' }
        ]
      },
    };

    const baseData: CommunityDetail = {
      id,
      name: 'Community',
      description: 'A vibrant community of crypto enthusiasts exploring the future of digital assets.',
      members: 1000,
      posts: 500,
      image: 'https://picsum.photos/200',
      coverImage: 'https://picsum.photos/800/400',
      joined: Math.random() > 0.5,
      rules: [
        'Be respectful to all members and maintain a positive environment',
        'No spam, self-promotion, or duplicate posts without permission',
        'Keep discussions relevant to the community topic',
        'No financial advice - always do your own research (DYOR)',
        'Report violations to moderators promptly'
      ],
      moderators: [
        { name: 'Community Mod', avatar: 'https://i.pravatar.cc/150?img=8', role: 'Moderator' }
      ],
      created: 'January 15, 2024',
      category: 'General',
      isPrivate: false,
      isPremium: false,
      onlineMembers: 125,
      membershipTier: 'free',
      badges: []
    };

    return { ...baseData, ...communities[id] };
  }, [id]);
};

// Enhanced post generation
const useCommunityPosts = (communityId: string, community: CommunityDetail | null) => {
  return useMemo(() => {
    if (!community) return [];

    const enhancedReactions: Reaction[] = [
      { emoji: '🚀', count: 0, isActive: false, color: '#FF6B6B' },
      { emoji: '💎', count: 0, isActive: false, color: '#4ECDC4' },
      { emoji: '📈', count: 0, isActive: false, color: '#45B7D1' },
      { emoji: '🔥', count: 0, isActive: false, color: '#FFA07A' },
      { emoji: '💯', count: 0, isActive: false, color: '#98D8C8' },
    ];

    const posts: CommunityPost[] = Array.from({ length: 10 }, (_, i) => ({
      id: `${communityId}-post-${i}`,
      author: `User_${i + 1}`,
      handle: `@user${i + 1}`,
      avatar: `https://i.pravatar.cc/150?img=${i + 1}`,
      date: `${Math.floor(Math.random() * 24)}h ago`,
      title: 'Community Discussion',
      content: `This is an engaging post about ${community.category}. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.`,
      reactions: enhancedReactions.map(r => ({
        ...r,
        count: Math.floor(Math.random() * 50)
      })),
      upvotes: Math.floor(Math.random() * 200),
      downvotes: Math.floor(Math.random() * 20),
      comments: [],
      isUpvoted: false,
      isDownvoted: false,
      isPinned: i === 0,
      moderatorPost: i % 5 === 0,
      tags: ['discussion', community.category.toLowerCase()],
    }));

    return posts;
  }, [communityId, community]);
};

// Enhanced components
const CommunityHeader: React.FC<{
  community: CommunityDetail;
  onJoinToggle: () => void;
  onShare: () => void;
}> = ({ community, onJoinToggle, onShare }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  return (
    <Animated.View style={[styles.headerContainer, { opacity: fadeAnim }]}>
      <ImageBackground
        source={{ uri: community.coverImage }}
        style={styles.coverImageBackground}
        resizeMode="cover"
      >
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)', 'rgba(0,0,0,0.9)']}
          style={styles.coverGradient}
        />
        
        <View style={styles.communityInfo}>
          <View style={styles.communityMainInfo}>
            <View style={styles.imageContainer}>
              <Image source={{ uri: community.image }} style={styles.communityImage} />
              {community.badges?.includes('Verified') && (
                <View style={styles.verifiedBadge}>
                  <Ionicons name="checkmark" size={12} color="#fff" />
                </View>
              )}
            </View>
            
            <View style={styles.communityDetails}>
              <View style={styles.nameRow}>
                <Text style={styles.communityName}>{community.name}</Text>
                {community.trending && (
                  <View style={styles.trendingBadge}>
                    <Ionicons name="trending-up" size={16} color="#FF6B6B" />
                    <Text style={styles.trendingText}>Trending</Text>
                  </View>
                )}
              </View>
              
              <View style={styles.badgesContainer}>
                {community.badges?.map((badge, index) => (
                  <View key={badge} style={styles.badge}>
                    <Text style={styles.badgeText}>{badge}</Text>
                  </View>
                ))}
              </View>
              
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <Ionicons name="people" size={16} color="#3EC1F9" />
                  <Text style={styles.statText}>{community.members?.toLocaleString()}</Text>
                </View>
                <View style={styles.statDivider} />
                <View style={styles.statItem}>
                  <Ionicons name="radio-button-on" size={16} color="#00D4AA" />
                  <Text style={styles.statText}>{community.onlineMembers} online</Text>
                </View>
                <View style={styles.statDivider} />
                <View style={styles.statItem}>
                  <Ionicons name="document-text" size={16} color="#FFA07A" />
                  <Text style={styles.statText}>{community.posts} posts</Text>
                </View>
              </View>
            </View>
          </View>
          
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.shareButton} onPress={onShare}>
              <Ionicons name="share-social" size={20} color="#fff" />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.joinButton, 
                community.joined && styles.joinedButton,
                community.isPremium && styles.premiumButton
              ]}
              onPress={onJoinToggle}
            >
              {community.isPremium && !community.joined && (
                <Ionicons name="diamond" size={16} color="#fff" style={{ marginRight: 6 }} />
              )}
              <Text style={[styles.joinButtonText, community.joined && styles.joinedButtonText]}>
                {community.joined 
                  ? 'Joined' 
                  : community.isPremium 
                    ? `Join $${community.price}/mo` 
                    : 'Join Community'
                }
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ImageBackground>
    </Animated.View>
  );
};

const EnhancedPostItem: React.FC<{
  post: CommunityPost;
  onUpvote: (id: string) => void;
  onDownvote: (id: string) => void;
  onReaction: (id: string, index: number) => void;
  onComment: (id: string) => void;
  onShare: () => void;
}> = ({ post, onUpvote, onDownvote, onReaction, onComment, onShare }) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePress = useCallback(() => {
    Animated.sequence([
      Animated.timing(scaleAnim, { toValue: 0.98, duration: 100, useNativeDriver: true }),
      Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();
  }, [scaleAnim]);

  return (
    <Animated.View style={[styles.postContainer, { transform: [{ scale: scaleAnim }] }]}>
      {post.isPinned && (
        <View style={styles.pinnedIndicator}>
          <Ionicons name="pin" size={16} color="#3EC1F9" />
          <Text style={styles.pinnedText}>Pinned Post</Text>
        </View>
      )}
      
      <View style={styles.postHeader}>
        <Image source={{ uri: post.avatar }} style={styles.avatar} />
        <View style={styles.postAuthor}>
          <View style={styles.authorRow}>
            <Text style={styles.postAuthorName}>{post.author}</Text>
            {post.moderatorPost && (
              <View style={styles.modBadge}>
                <Text style={styles.modBadgeText}>MOD</Text>
              </View>
            )}
          </View>
          <Text style={styles.postHandle}>{post.handle} • {post.date}</Text>
        </View>
        
        <TouchableOpacity style={styles.optionsButton}>
          <Ionicons name="ellipsis-horizontal" size={20} color="#666" />
        </TouchableOpacity>
      </View>
      
      <Text style={styles.postContent}>{post.content}</Text>
      
      {post.tags && (
        <View style={styles.tagsContainer}>
          {post.tags.map((tag, index) => (
            <View key={tag + index} style={styles.tag}>
              <Text style={styles.tagText}>#{tag}</Text>
            </View>
          ))}
        </View>
      )}
      
      <View style={styles.engagementContainer}>
        <View style={styles.reactionsRow}>
          {post.reactions.map((reaction, index) => (
            <TouchableOpacity
              key={`${post.id}-reaction-${index}`}
              style={[styles.reaction, reaction.isActive && styles.activeReaction]}
              onPress={() => onReaction(post.id, index)}
            >
              <Text style={styles.reactionEmoji}>{reaction.emoji}</Text>
              <Text style={[styles.reactionCount, reaction.isActive && styles.activeReactionCount]}>
                {reaction.count}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        
        <View style={styles.postActions}>
          <TouchableOpacity 
            style={[styles.actionButton, post.isUpvoted && styles.activeAction]}
            onPress={() => onUpvote(post.id)}
          >
            <Ionicons 
              name={post.isUpvoted ? 'arrow-up-circle' : 'arrow-up-circle-outline'} 
              size={22} 
              color={post.isUpvoted ? '#3EC1F9' : '#666'} 
            />
            <Text style={[styles.actionCount, post.isUpvoted && styles.activeActionText]}>
              {post.upvotes}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, post.isDownvoted && styles.activeAction]}
            onPress={() => onDownvote(post.id)}
          >
            <Ionicons 
              name={post.isDownvoted ? 'arrow-down-circle' : 'arrow-down-circle-outline'} 
              size={22} 
              color={post.isDownvoted ? '#FF3B30' : '#666'} 
            />
            <Text style={[styles.actionCount, post.isDownvoted && { color: '#FF3B30' }]}>
              {post.downvotes}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => onComment(post.id)}
          >
            <Ionicons name="chatbubble-outline" size={20} color="#666" />
            <Text style={styles.actionCount}>{post.comments?.length || 0}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={onShare}>
            <Ionicons name="share-social-outline" size={20} color="#666" />
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
};

const PostComposer: React.FC<{
  value: string;
  onChangeText: (text: string) => void;
  onPost: () => void;
  onCancel: () => void;
  isPosting: boolean;
  community: CommunityDetail;
}> = ({ value, onChangeText, onPost, onCancel, isPosting, community }) => {
  const [isFocused, setIsFocused] = useState(false);
  const heightAnim = useRef(new Animated.Value(60)).current;

  useEffect(() => {
    Animated.timing(heightAnim, {
      toValue: isFocused ? 140 : 60,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isFocused, heightAnim]);

  return (
    <Animated.View style={[styles.composerContainer, { minHeight: heightAnim }]}>
      <View style={styles.composerHeader}>
        <Image 
          source={{ uri: 'https://i.pravatar.cc/150?img=1' }} 
          style={styles.composerAvatar} 
        />
        <Text style={styles.composerText}>Share with {community.name}</Text>
      </View>
      
      <TextInput
        style={styles.composerInput}
        placeholder="What's happening in the community?"
        placeholderTextColor="#666"
        value={value}
        onChangeText={onChangeText}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        multiline
        maxLength={280}
      />
      
      {isFocused && (
        <View style={styles.composerActions}>
          <View style={styles.composerTools}>
            <TouchableOpacity style={styles.toolButton}>
              <Ionicons name="image-outline" size={20} color="#3EC1F9" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.toolButton}>
              <Ionicons name="link-outline" size={20} color="#3EC1F9" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.toolButton}>
              <Ionicons name="pricetag-outline" size={20} color="#3EC1F9" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.composerButtons}>
            <Text style={styles.charCount}>{value.length}/280</Text>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.postButton, (!value.trim() || isPosting) && styles.postButtonDisabled]}
              onPress={onPost}
              disabled={!value.trim() || isPosting}
            >
              {isPosting ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.postButtonText}>Post</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      )}
    </Animated.View>
  );
};

// Main component
export default function CommunityDetailScreen() {
  const { id } = useLocalSearchParams();
  const communityId = Array.isArray(id) ? id[0] : id || '';
  
  // State management
  const community = useCommunityDetail(communityId);
  const [activeTab, setActiveTab] = useState<'posts' | 'about' | 'members'>('posts');
  const [isComposing, setIsComposing] = useState(false);
  const [postText, setPostText] = useState('');
  const [isPosting, setIsPosting] = useState(false);
  
  // Enhanced loading states
  const { isInitialLoading, isRefreshing, setIsInitialLoading, setIsRefreshing } = useLoadingStates();
  
  // Posts data
  const posts = useCommunityPosts(communityId, community);
  const [localPosts, setLocalPosts] = useState<CommunityPost[]>(posts);
  
  useEffect(() => {
    setLocalPosts(posts);
  }, [posts]);

  // Enhanced interaction handlers
  const handleUpvote = useCallback((postId: string) => {
    setLocalPosts(current => 
      current.map(post => 
        post.id === postId 
          ? { 
              ...post, 
              isUpvoted: !post.isUpvoted,
              upvotes: post.isUpvoted ? post.upvotes - 1 : post.upvotes + 1,
              isDownvoted: false,
              downvotes: post.isDownvoted ? post.downvotes - 1 : post.downvotes
            } 
          : post
      )
    );
  }, []);
  
  const handleDownvote = useCallback((postId: string) => {
    setLocalPosts(current => 
      current.map(post => 
        post.id === postId 
          ? { 
              ...post, 
              isDownvoted: !post.isDownvoted,
              downvotes: post.isDownvoted ? post.downvotes - 1 : post.downvotes + 1,
              isUpvoted: false,
              upvotes: post.isUpvoted ? post.upvotes - 1 : post.upvotes
            } 
          : post
      )
    );
  }, []);
  
  const handleReaction = useCallback((postId: string, reactionIndex: number) => {
    setLocalPosts(current => 
      current.map(post => {
        if (post.id === postId) {
          const updatedReactions = [...post.reactions];
          const reaction = updatedReactions[reactionIndex];
          if (reaction) {
            updatedReactions[reactionIndex] = {
              ...reaction,
              count: reaction.isActive ? reaction.count - 1 : reaction.count + 1,
              isActive: !reaction.isActive
            };
          }
          return { ...post, reactions: updatedReactions };
        }
        return post;
      })
    );
  }, []);

  const handleJoinToggle = useCallback(() => {
    if (community.isPremium && !community.joined) {
      Alert.alert(
        'Premium Community',
        `Join ${community.name} for $${community.price}/month and unlock exclusive content, priority support, and premium features.`,
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Subscribe', 
            onPress: () => {
              // Handle premium subscription
              Alert.alert('Success', 'Welcome to the premium community!');
            }
          }
        ]
      );
    } else {
      // Handle regular join/leave
      Alert.alert(
        community.joined ? 'Leave Community' : 'Join Community',
        community.joined 
          ? `Are you sure you want to leave ${community.name}?`
          : `Join ${community.name} and connect with ${community.members} members!`,
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: community.joined ? 'Leave' : 'Join',
            onPress: () => {
              // Handle join/leave logic
            }
          }
        ]
      );
    }
  }, [community]);

  const handleShare = useCallback(async () => {
    try {
      await Share.share({
        message: `Check out ${community.name} on HodlHub!\n\n${community.description}\n\nJoin ${community.members} members discussing ${community.category}.`,
        title: community.name
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  }, [community]);

  const handleCreatePost = useCallback(async () => {
    if (!postText.trim() || isPosting) return;
    
    setIsPosting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const newPost: CommunityPost = {
        id: `user-post-${Date.now()}`,
        author: 'You',
        handle: '@you',
        avatar: 'https://i.pravatar.cc/150?img=1',
        date: 'Just now',
        title: 'Community Post',
        content: postText.trim(),
        reactions: [
          { emoji: '🚀', count: 0, isActive: false, color: '#FF6B6B' },
          { emoji: '💎', count: 0, isActive: false, color: '#4ECDC4' },
          { emoji: '📈', count: 0, isActive: false, color: '#45B7D1' },
          { emoji: '🔥', count: 0, isActive: false, color: '#FFA07A' },
          { emoji: '💯', count: 0, isActive: false, color: '#98D8C8' },
        ],
        upvotes: 0,
        downvotes: 0,
        comments: [],
        isUpvoted: false,
        isDownvoted: false,
        tags: ['discussion']
      };
      
      setLocalPosts(current => [newPost, ...current]);
      setPostText('');
      setIsComposing(false);
      
    } catch (err) {
      Alert.alert('Error', 'Failed to post. Please try again.');
    } finally {
      setIsPosting(false);
    }
  }, [postText, isPosting]);

  // Loading state
  if (isInitialLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <LoadingWhale size={60} />
          <Text style={styles.loadingText}>Loading community...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Enhanced Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle} numberOfLines={1}>{community.name}</Text>
        <TouchableOpacity style={styles.moreButton}>
          <Ionicons name="ellipsis-vertical" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Community Header */}
      <CommunityHeader 
        community={community}
        onJoinToggle={handleJoinToggle}
        onShare={handleShare}
      />

      {/* Enhanced Tabs */}
      <View style={styles.tabsContainer}>
        {['posts', 'about', 'members'].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tab, activeTab === tab && styles.activeTab]}
            onPress={() => setActiveTab(tab as any)}
          >
            <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
            {activeTab === tab && <View style={styles.tabIndicator} />}
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      {activeTab === 'posts' && (
        <FlatList
          data={localPosts}
          renderItem={({ item }) => (
            <EnhancedPostItem
              post={item}
              onUpvote={handleUpvote}
              onDownvote={handleDownvote}
              onReaction={handleReaction}
              onComment={(id) => console.log('Comment on:', id)}
              onShare={handleShare}
            />
          )}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.postsContainer}
          ListHeaderComponent={
            community.joined ? (
              <PostComposer
                value={postText}
                onChangeText={setPostText}
                onPost={handleCreatePost}
                onCancel={() => setIsComposing(false)}
                isPosting={isPosting}
                community={community}
              />
            ) : null
          }
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={() => {
                setIsRefreshing(true);
                setTimeout(() => setIsRefreshing(false), 1000);
              }}
              tintColor="#3EC1F9"
              colors={['#3EC1F9']}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      )}

      {activeTab === 'about' && (
        <ScrollView style={styles.aboutContainer} showsVerticalScrollIndicator={false}>
          <View style={styles.aboutSection}>
            <Text style={styles.aboutTitle}>About</Text>
            <Text style={styles.aboutDescription}>{community.description}</Text>
          </View>
          
          <View style={styles.aboutSection}>
            <Text style={styles.aboutTitle}>Community Rules</Text>
            {community.rules.map((rule, index) => (
              <View key={index} style={styles.ruleItem}>
                <Text style={styles.ruleNumber}>{index + 1}.</Text>
                <Text style={styles.ruleText}>{rule}</Text>
              </View>
            ))}
          </View>
          
          <View style={styles.aboutSection}>
            <Text style={styles.aboutTitle}>Moderators</Text>
            {community.moderators.map((mod, index) => (
              <View key={index} style={styles.moderatorItem}>
                <Image source={{ uri: mod.avatar }} style={styles.moderatorAvatar} />
                <View style={styles.moderatorInfo}>
                  <Text style={styles.moderatorName}>{mod.name}</Text>
                  <Text style={styles.moderatorRole}>{mod.role}</Text>
                </View>
                <View style={styles.modBadge}>
                  <Text style={styles.modBadgeText}>MOD</Text>
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
      )}

      {activeTab === 'members' && (
        <View style={styles.membersContainer}>
          <Text style={styles.membersTitle}>Members</Text>
          <Text style={styles.membersSubtitle}>
            Connect with {community.members} community members
          </Text>
          {/* Members list would go here */}
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#000',
    borderBottomWidth: 1,
    borderBottomColor: '#1a1a1a',
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  moreButton: {
    padding: 8,
    marginLeft: 8,
  },
  headerTitle: {
    flex: 1,
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  
  // Enhanced Header Styles
  headerContainer: {
    backgroundColor: '#000',
  },
  coverImageBackground: {
    height: 200,
    justifyContent: 'flex-end',
  },
  coverGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  communityInfo: {
    padding: 16,
    paddingTop: 24,
  },
  communityMainInfo: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 16,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  communityImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    borderColor: '#000',
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#3EC1F9',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#000',
  },
  communityDetails: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  communityName: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
  },
  trendingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 107, 107, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  trendingText: {
    color: '#FF6B6B',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  badgesContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  badge: {
    backgroundColor: 'rgba(62, 193, 249, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 8,
  },
  badgeText: {
    color: '#3EC1F9',
    fontSize: 12,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    color: '#ccc',
    fontSize: 14,
    marginLeft: 4,
  },
  statDivider: {
    width: 1,
    height: 12,
    backgroundColor: '#666',
    marginHorizontal: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  shareButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  joinButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3EC1F9',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 24,
    minWidth: 120,
    justifyContent: 'center',
  },
  joinedButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#3EC1F9',
  },
  premiumButton: {
    backgroundColor: 'linear-gradient(45deg, #3EC1F9, #4ECDC4)',
  },
  joinButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  joinedButtonText: {
    color: '#3EC1F9',
  },
  
  // Enhanced Tabs
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#000',
    borderBottomWidth: 1,
    borderBottomColor: '#1a1a1a',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    position: 'relative',
  },
  activeTab: {
    backgroundColor: 'rgba(62, 193, 249, 0.05)',
  },
  tabText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  activeTabText: {
    color: '#3EC1F9',
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 3,
    backgroundColor: '#3EC1F9',
    borderRadius: 2,
  },
  
  // Enhanced Post Styles
  postsContainer: {
    padding: 16,
  },
  postContainer: {
    backgroundColor: '#1a1a1a',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#2a2a2a',
  },
  pinnedIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: 'rgba(62, 193, 249, 0.1)',
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  pinnedText: {
    color: '#3EC1F9',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginRight: 12,
  },
  postAuthor: {
    flex: 1,
  },
  authorRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  postAuthorName: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  modBadge: {
    backgroundColor: '#3EC1F9',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    marginLeft: 8,
  },
  modBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  postHandle: {
    color: '#666',
    fontSize: 14,
    marginTop: 2,
  },
  optionsButton: {
    padding: 8,
  },
  postContent: {
    color: '#fff',
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  tag: {
    backgroundColor: 'rgba(62, 193, 249, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 4,
  },
  tagText: {
    color: '#3EC1F9',
    fontSize: 12,
    fontWeight: '500',
  },
  engagementContainer: {
    borderTopWidth: 1,
    borderTopColor: '#2a2a2a',
    paddingTop: 12,
  },
  reactionsRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  reaction: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2a2a2a',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  activeReaction: {
    backgroundColor: 'rgba(62, 193, 249, 0.2)',
    borderWidth: 1,
    borderColor: '#3EC1F9',
  },
  reactionEmoji: {
    fontSize: 16,
    marginRight: 4,
  },
  reactionCount: {
    color: '#666',
    fontSize: 12,
    fontWeight: '600',
  },
  activeReactionCount: {
    color: '#3EC1F9',
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 12,
  },
  activeAction: {
    backgroundColor: 'rgba(62, 193, 249, 0.1)',
  },
  actionCount: {
    color: '#666',
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '600',
  },
  activeActionText: {
    color: '#3EC1F9',
  },
  
  // Enhanced Composer
  composerContainer: {
    backgroundColor: '#1a1a1a',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#2a2a2a',
  },
  composerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  composerAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    marginRight: 12,
  },
  composerText: {
    color: '#666',
    fontSize: 16,
  },
  composerInput: {
    color: '#fff',
    fontSize: 16,
    minHeight: 40,
    textAlignVertical: 'top',
  },
  composerActions: {
    marginTop: 12,
  },
  composerTools: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  toolButton: {
    padding: 8,
    marginRight: 16,
  },
  composerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  charCount: {
    color: '#666',
    fontSize: 12,
  },
  cancelButton: {
    padding: 8,
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
  },
  postButton: {
    backgroundColor: '#3EC1F9',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
  },
  postButtonDisabled: {
    opacity: 0.5,
  },
  postButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  
  // About Tab
  aboutContainer: {
    flex: 1,
    padding: 16,
  },
  aboutSection: {
    marginBottom: 24,
  },
  aboutTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  aboutDescription: {
    color: '#ccc',
    fontSize: 16,
    lineHeight: 24,
  },
  ruleItem: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  ruleNumber: {
    color: '#3EC1F9',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 12,
    minWidth: 24,
  },
  ruleText: {
    color: '#ccc',
    fontSize: 16,
    lineHeight: 24,
    flex: 1,
  },
  moderatorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    marginBottom: 8,
  },
  moderatorAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  moderatorInfo: {
    flex: 1,
  },
  moderatorName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  moderatorRole: {
    color: '#666',
    fontSize: 14,
    marginTop: 2,
  },
  
  // Members Tab
  membersContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  membersTitle: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  membersSubtitle: {
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
});