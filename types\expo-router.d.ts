import { Link, LinkProps, PathConfig } from 'expo-router';

declare module 'expo-router' {
  interface Router {
    navigate: (path: string) => void;
    push: (path: string | { pathname: string; params?: Record<string, any> }) => void;
    replace: (path: string) => void;
  }
}

declare global {
  namespace ReactNavigation {
    interface RootParamList {
      // Tabs
      '/(tabs)': undefined;
      '/(tabs)/wallet': undefined;
      '/(tabs)/community': undefined;
      '/(tabs)/rankings': undefined;
      '/(tabs)/profile': undefined;
      '/(tabs)/messenger': undefined;
      
      // Custom routes
      '/(tabs)/select-friend': undefined;
      '/chat': {
        userId: string;
        name: string;
        avatar: string;
        handle: string;
        online: string;
      };
      
      // Add other custom routes here as needed
    }
  }
}

// Extend the expo-router's Link component props
declare module 'expo-router' {
  interface LinkProps extends Omit<LinkProps, 'href'> {
    href: keyof ReactNavigation.RootParamList | string;
  }
}
