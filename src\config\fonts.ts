import * as Font from 'expo-font';

export const loadFonts = async () => {
  await Font.loadAsync({
    'Inter_400Regular': require('../../assets/fonts/Inter-Regular.ttf'),
    'Inter_500Medium': require('../../assets/fonts/Inter-Medium.ttf'),
    'Inter_600SemiBold': require('../../assets/fonts/Inter-SemiBold.ttf'),
    'Inter_700Bold': require('../../assets/fonts/Inter-Bold.ttf'),
  });
};

export const fontConfig = {
  fontFamily: 'Inter_400Regular',
  h1: {
    fontFamily: 'Inter_700Bold',
    fontSize: 32,
    lineHeight: 40,
  },
  h2: {
    fontFamily: 'Inter_600SemiBold',
    fontSize: 28,
    lineHeight: 36,
  },
  h3: {
    fontFamily: 'Inter_600SemiBold',
    fontSize: 24,
    lineHeight: 32,
  },
  h4: {
    fontFamily: 'Inter_600SemiBold',
    fontSize: 20,
    lineHeight: 28,
  },
  body: {
    fontFamily: 'Inter_400Regular',
    fontSize: 16,
    lineHeight: 24,
  },
  body2: {
    fontFamily: 'Inter_400Regular',
    fontSize: 14,
    lineHeight: 20,
  },
  caption: {
    fontFamily: 'Inter_400Regular',
    fontSize: 12,
    lineHeight: 16,
  },
  button: {
    fontFamily: 'Inter_600SemiBold',
    fontSize: 16,
    lineHeight: 24,
  },
  label: {
    fontFamily: 'Inter_600SemiBold',
    fontSize: 14,
    lineHeight: 20,
    textTransform: 'uppercase' as const,
    letterSpacing: 0.5,
  },
} as const;
