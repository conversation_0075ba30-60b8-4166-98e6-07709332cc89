import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  SafeAreaView,
  Text,
  TouchableOpacity,
  ListRenderItem,
  ViewToken,
  TextInput,
  Image,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { useFocusEffect } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import WhaleLoader from '../../../components/WhaleLoader';

// Hooks
import { useCommunities } from '@src/hooks/useCommunities';
import { useTheme } from '@src/hooks/useTheme';

// Components
import { CommunityItem } from '@src/components/community/CommunityItem';

// Types
import { Community } from '@src/types/community';

// Constants
const ITEM_HEIGHT = 120;
const LOADING_ITEMS = 6;
const VIEWABILITY_CONFIG = {
  itemVisiblePercentThreshold: 50,
  minimumViewTime: 300,
};

interface ViewableItemsChanged {
  viewableItems: ViewToken[];
  changed: ViewToken[];
}

// Enhanced Header component with create button
const EnhancedHeader = React.memo(({ 
  searchQuery, 
  onSearchChange,
  onCreatePress 
}: { 
  searchQuery: string; 
  onSearchChange: (text: string) => void;
  onCreatePress: () => void;
}) => (
  <View style={styles.headerContainer}>
    {/* Title and Create Button Row */}
    <View style={styles.titleRow}>
      <Text style={styles.title} accessibilityRole="header">
        Communities
      </Text>
      <TouchableOpacity 
        style={styles.createIconButton}
        onPress={onCreatePress}
        accessibilityRole="button"
        accessibilityLabel="Create new community"
        accessibilityHint="Navigate to create community screen"
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
    
    {/* Create Community Button */}
    <TouchableOpacity 
      style={styles.createButton}
      onPress={onCreatePress}
      accessibilityRole="button"
      accessibilityLabel="Create new community"
    >
      <Ionicons name="add-circle-outline" size={20} color="#fff" style={styles.createButtonIcon} />
      <Text style={styles.createButtonText}>Create Community</Text>
    </TouchableOpacity>
    
    {/* Search Container */}
    <View style={styles.searchContainer}>
      <Ionicons name="search" size={20} color="#666" />
      <TextInput
        style={styles.searchInput}
        placeholder="Search communities..."
        placeholderTextColor="#666"
        value={searchQuery}
        onChangeText={onSearchChange}
        returnKeyType="search"
        clearButtonMode="while-editing"
        accessibilityLabel="Search communities"
      />
    </View>
  </View>
));

EnhancedHeader.displayName = 'EnhancedHeader';

// Footer component for loading more indicator
const ListFooter = React.memo(({ 
  isLoadingMore 
}: { 
  isLoadingMore: boolean 
}) => {
  if (!isLoadingMore) return null;
  
  return (
    <View 
      style={styles.footerContainer}
      accessibilityElementsHidden={!isLoadingMore}
    >
      <ActivityIndicator size={24} color="#666" />
    </View>
  );
});

ListFooter.displayName = 'ListFooter';

// Empty state when no communities are found
const EmptyState = React.memo(({ 
  onRefresh, 
  isRefreshing,
  searchQuery,
  hasError = false,
  onCreatePress
}: { 
  onRefresh: () => void;
  isRefreshing: boolean;
  searchQuery: string;
  hasError?: boolean;
  onCreatePress: () => void;
}) => (
  <View 
    style={styles.emptyState}
    accessibilityElementsHidden={isRefreshing}
  >
    <Ionicons 
      name="people" 
      size={64} 
      color="#666" 
      accessible={false}
    />
    <Text style={styles.emptyStateText}>
      {searchQuery.trim() 
        ? 'No communities match your search.'
        : 'No communities found.'}
    </Text>
    
    {!searchQuery.trim() && (
      <TouchableOpacity 
        style={[styles.actionButton, styles.emptyStateCreateButton]}
        onPress={onCreatePress}
        accessibilityRole="button"
        accessibilityLabel="Create the first community"
      >
        <Ionicons name="add" size={16} color="#fff" />
        <Text style={styles.emptyStateCreateButtonText}>
          Create the first community
        </Text>
      </TouchableOpacity>
    )}
    
    <TouchableOpacity 
      style={styles.refreshButton}
      onPress={onRefresh}
      disabled={isRefreshing}
      accessibilityRole="button"
      accessibilityLabel={isRefreshing ? 'Refreshing' : 'Refresh communities'}
    >
      <Text style={styles.refreshButtonText}>
        {isRefreshing ? 'Refreshing...' : 'Refresh'}
      </Text>
    </TouchableOpacity>
  </View>
));

EmptyState.displayName = 'EmptyState';

// Skeleton loader for community items
const CommunitySkeleton = React.memo(() => (
  <View 
    style={styles.skeletonContainer}
    accessibilityLabel="Loading community"
    accessibilityHint="Content is loading"
  >
    <View style={styles.skeletonImage} />
    <View style={styles.skeletonContent}>
      <View style={styles.skeletonTitle} />
      <View style={styles.skeletonDescription} />
      <View style={styles.skeletonMembers} />
    </View>
    <View style={styles.skeletonButton} />
  </View>
));

CommunitySkeleton.displayName = 'CommunitySkeleton';

// Skeleton loader list for initial loading
const SkeletonList = React.memo(() => (
  <View style={styles.skeletonListContainer}>
    {Array.from({ length: LOADING_ITEMS }).map((_, index) => (
      <CommunitySkeleton key={`skeleton-${index}`} />
    ))}
  </View>
));

// Main component
export default function CommunitiesScreen() {
  const router = useRouter();
  const { theme } = useTheme();
  const { colors } = theme;
  const {
    communities,
    filteredCommunities,
    searchQuery,
    isLoading,
    isRefreshing,
    isLoadingMore,
    handleSearch,
    handleRefresh,
    loadMoreCommunities,
    handleJoinToggle,
  } = useCommunities();

  // Handle navigation to create community screen
  const handleCreatePress = useCallback(() => {
    router.push('/create');
  }, [router]);

  // Handle search with debounce
  const handleSearchChange = useCallback((text: string) => {
    handleSearch(text);
  }, [handleSearch]);

  // Handle pull to refresh
  const onRefresh = useCallback(async () => {
    await handleRefresh();
  }, [handleRefresh]);

  // Handle infinite scroll
  const onEndReached = useCallback(() => {
    if (!isLoading && !isRefreshing && !isLoadingMore) {
      loadMoreCommunities();
    }
  }, [isLoading, isRefreshing, isLoadingMore, loadMoreCommunities]);

  // Render each community item
  const renderItem = useCallback(({ item }: { item: Community }) => (
    <CommunityItem 
      item={item} 
      onJoinToggle={handleJoinToggle} 
    />
  ), [handleJoinToggle]);

  // Memoize the list key extractor
  const extractKey = useCallback((item: Community) => item.id, []);

  // Get item layout for optimization
  const getItemLayout = useCallback((data: Community[] | null | undefined, index: number) => ({
    length: ITEM_HEIGHT,
    offset: ITEM_HEIGHT * index,
    index,
  }), []);

  // Loading state component using the app's LoadingState
  const FullScreenLoader = () => (
    <View style={styles.fullScreenLoaderContainer}>
      <WhaleLoader size={100} />
    </View>
  );
  
  // Track initial load state
  const isInitialLoading = isLoading && filteredCommunities.length === 0;

  // Render empty state
  const renderEmptyState = useCallback(() => {
    const hasError = false; // TODO: Get error state from your data fetching logic
    
    return (
      <EmptyState 
        onRefresh={onRefresh} 
        isRefreshing={isRefreshing} 
        searchQuery={searchQuery}
        hasError={hasError}
        onCreatePress={handleCreatePress}
      />
    );
  }, [onRefresh, isRefreshing, searchQuery, handleCreatePress]);

  // Show full screen loading for initial load
  if (isInitialLoading) {
    return <FullScreenLoader />;
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Enhanced Header with Create Button */}
      <EnhancedHeader 
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
        onCreatePress={handleCreatePress}
      />
      
      {/* Main Content */}
      {isLoading && !isRefreshing ? (
        <SkeletonList />
      ) : (
        <FlatList
          data={filteredCommunities}
          renderItem={renderItem}
          keyExtractor={extractKey}
          contentContainerStyle={[
            styles.listContainer,
            filteredCommunities.length === 0 && styles.emptyListContentContainer
          ]}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
              accessibilityLabel="Pull to refresh"
              accessibilityRole="button"
            />
          }
          onEndReached={onEndReached}
          onEndReachedThreshold={0.5}
          ListEmptyComponent={!isLoading ? renderEmptyState() : null}
          ListFooterComponent={
            isLoadingMore ? (
              <View style={styles.loadingMoreContainer}>
                <ActivityIndicator size={24} color="#3EC1F9" />
              </View>
            ) : null
          }
          showsVerticalScrollIndicator={false}
          removeClippedSubviews
          maxToRenderPerBatch={10}
          updateCellsBatchingPeriod={50}
          initialNumToRender={10}
          windowSize={10}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  // Layout
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  contentContainer: {
    flex: 1,
  },
  fullScreenLoaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  
  // Enhanced Header
  headerContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: 'transparent',
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
  },
  createIconButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#3EC1F9',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#3EC1F9',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  
  // Create Button
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3EC1F9',
    paddingHorizontal: 20,
    paddingVertical: 14,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#3EC1F9',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  createButtonIcon: {
    marginRight: 8,
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  
  // Search
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1A1A1A',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#333',
    marginBottom: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    color: '#fff',
    fontSize: 16,
    fontWeight: '400',
    paddingVertical: 0,
  },
  
  // List
  listContainer: {
    paddingBottom: 24,
    paddingTop: 8,
  },
  emptyListContentContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  skeletonListContainer: {
    paddingTop: 8,
  },
  footerContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingMoreContainer: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  
  // Empty States
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    minHeight: 400,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
    lineHeight: 22,
  },
  emptyStateCreateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3EC1F9',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 0,
  },
  emptyStateCreateButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8,
  },
  refreshButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3EC1F9',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  refreshButtonText: {
    color: '#3EC1F9',
    fontWeight: '600',
    fontSize: 16,
  },
  
  // Loading States - Skeletons
  skeletonContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 12,
    alignItems: 'center',
    height: 108,
  },
  skeletonImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  skeletonContent: {
    flex: 1,
    marginLeft: 16,
    justifyContent: 'space-between',
  },
  skeletonTitle: {
    width: '60%',
    height: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    marginBottom: 8,
  },
  skeletonDescription: {
    width: '90%',
    height: 14,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    marginBottom: 8,
  },
  skeletonMembers: {
    width: '40%',
    height: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
  },
  skeletonButton: {
    width: 64,
    height: 32,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    marginLeft: 8,
  },
  
  // General Buttons
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3EC1F9',
    minWidth: 64,
    alignItems: 'center',
    justifyContent: 'center',
  },
});