import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Switch, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, router } from 'expo-router';

const settings = [
  {
    title: 'Account',
    items: [
      { icon: 'person-outline', label: 'Profile Information', type: 'link' },
      { icon: 'lock-closed-outline', label: 'Security', type: 'link' },
      { icon: 'notifications-outline', label: 'Notifications', type: 'toggle' },
    ],
  },
  {
    title: 'Trading',
    items: [
      { icon: 'wallet-outline', label: 'Default Portfolio', type: 'link' },
      { icon: 'key-outline', label: 'API Keys', type: 'link' },
      { icon: 'trending-up-outline', label: 'Trading Preferences', type: 'link' },
    ],
  },
  {
    title: 'App',
    items: [
      { icon: 'moon-outline', label: 'Dark Mode', type: 'toggle' },
      { icon: 'language-outline', label: 'Language', type: 'link' },
      { icon: 'information-circle-outline', label: 'About', type: 'link' },
    ],
  },
];

export default function SettingsScreen() {
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [darkModeEnabled, setDarkModeEnabled] = useState(true);

  const handleLinkPress = (label: string) => {
    if (label === 'Profile Information') {
      console.log('Navigate to_PROFILE_INFORMATION_SCREEN');
      // router.push('/profile'); // Example of actual navigation
    } else if (label === 'Security') {
      console.log('Navigate to_SECURITY_SCREEN');
    } else {
      console.log('Link pressed:', label);
    }
  };

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.title}>Settings</Text>
        </View>

        <ScrollView style={styles.content}>
          {settings.map((section, sectionIndex) => (
            <View key={sectionIndex} style={styles.section}>
              <Text style={styles.sectionTitle}>{section.title}</Text>
              {section.items.map((item, itemIndex) => (
                <TouchableOpacity
                  key={itemIndex}
                  style={styles.settingItem}
                  onPress={() => item.type === 'link' ? handleLinkPress(item.label) : null}
                  activeOpacity={item.type === 'toggle' ? 1 : 0.2} // No opacity change for toggle rows
                >
                  <View style={styles.settingLeft}>
                    <Ionicons name={item.icon as any} size={24} color="#fff" />
                    <Text style={styles.settingLabel}>{item.label}</Text>
                  </View>
                  {item.type === 'toggle' ? (
                    <Switch
                      value={item.label === 'Notifications' ? notificationsEnabled : darkModeEnabled}
                      onValueChange={item.label === 'Notifications' ? setNotificationsEnabled : setDarkModeEnabled}
                      trackColor={{ false: '#333', true: '#0047AB' }}
                      thumbColor={(item.label === 'Notifications' ? notificationsEnabled : darkModeEnabled) ? '#fff' : '#f4f3f4'}
                    />
                  ) : (
                    <Ionicons name="chevron-forward" size={24} color="#666" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          ))}
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
    backgroundColor: '#111',
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  section: {
    marginTop: 24,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 16,
    textTransform: 'uppercase',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#222',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingLabel: {
    fontSize: 16,
    color: '#fff',
    marginLeft: 16,
  },
});