import { AppTheme } from './types';

export const darkTheme: AppTheme = {
  name: 'dark',
  colors: {
    primary: '#3EC1F9',
    secondary: '#5A9BD5',
    accent: '#FFC000',
    background: {
      primary: '#000000',
      secondary: '#1C1C1E',
      tertiary: '#2C2C2E',
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#8E8E93',
      tertiary: '#48484A',
      disabled: '#3A3A3C',
      inverse: '#FFFFFF',
    },
    border: '#38383A',
    success: '#30D158',
    error: '#FF453A',
    warning: '#FF9F0A',
    positive: '#28A745',
    negative: '#DC3545',
    neutral: '#8E8E93',
    skeletonBackground: '#2C2C2E',
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32 },
  typography: { 
    h1: { fontSize: 32, fontWeight: 'bold' }, 
    body: { fontSize: 16, fontWeight: 'normal' } 
  },
  radii: { sm: 4, md: 8, lg: 16, full: 999 },
  shadows: {},
};
