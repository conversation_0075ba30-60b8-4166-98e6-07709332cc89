import React, { createContext, useContext, useState, useMemo, useEffect, ReactNode } from 'react';
import { useColorScheme } from 'react-native';

// Enhanced color system based on working app visuals
const lightTheme = {
  colors: {
    // Primary brand colors (from app images)
    primary: '#00D4FF',      // <PERSON>an blue (buttons, links, active states)
    secondary: '#0099CC',    // Darker cyan
    tertiary: '#66E5FF',     // Light cyan
    
    // Background system
    background: '#FFFFFF',
    backgroundSecondary: '#F5F5F5',
    surface: '#FFFFFF',
    surfaceSecondary: '#F8F9FA',
    
    // Text system
    text: '#000000',
    textSecondary: '#666666',
    textTertiary: '#888888',
    
    // Status colors (from portfolio rankings)
    success: '#00FF88',      // Green gains (+287.5%)
    error: '#FF4444',        // Red losses
    warning: '#FFB800',
    info: '#00D4FF',
    
    // UI elements
    border: '#E0E0E0',
    borderSecondary: '#F0F0F0',
    disabled: '#CCCCCC',
    overlay: 'rgba(0, 0, 0, 0.5)',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 6,
    md: 12,        // Main border radius from app
    lg: 16,
    xl: 24,
    full: 9999,    // For avatars/pills
  },
  typography: {
    h1: { fontSize: 32, fontWeight: 'bold' },
    h2: { fontSize: 24, fontWeight: 'bold' },
    h3: { fontSize: 20, fontWeight: '600' },
    body: { fontSize: 16, fontWeight: '400' },
    caption: { fontSize: 14, fontWeight: '400' },
    small: { fontSize: 12, fontWeight: '400' },
  },
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 4,
    },
  },
};

const darkTheme = {
  colors: {
    // Primary brand colors (matching working app)
    primary: '#00D4FF',      // Same cyan - brand consistency
    secondary: '#0099CC',
    tertiary: '#66E5FF',
    
    // Dark background system (from app screenshots)
    background: '#000000',        // Pure black background
    backgroundSecondary: '#0A0A0A',
    surface: '#1A1A1A',          // Card backgrounds from app
    surfaceSecondary: '#2A2A2A',
    
    // Dark text system (from app screenshots)
    text: '#FFFFFF',             // White primary text
    textSecondary: '#CCCCCC',    
    textTertiary: '#888888',     // Gray timestamps/subtitles
    
    // Status colors (same as light for consistency)
    success: '#00FF88',          // Green from portfolio gains
    error: '#FF4444',            // Red from losses
    warning: '#FFB800',
    info: '#00D4FF',
    
    // Dark UI elements
    border: '#333333',
    borderSecondary: '#222222',
    disabled: '#444444',
    overlay: 'rgba(0, 0, 0, 0.8)',
  },
  spacing: lightTheme.spacing,     // Same spacing system
  borderRadius: lightTheme.borderRadius,  // Same border radius
  typography: lightTheme.typography,      // Same typography
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.4,
      shadowRadius: 8,
      elevation: 4,
    },
  },
};

type ThemePreference = 'light' | 'dark' | 'system';
type AppTheme = typeof lightTheme;

interface ThemeContextType {
  theme: AppTheme;
  themePreference: ThemePreference;
  isDark: boolean;
  setTheme: (theme: ThemePreference) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const systemColorScheme = useColorScheme();
  const [themePreference, setThemePreference] = useState<ThemePreference>('system');

  // Determine if dark mode should be active
  const isDark = themePreference === 'system'
    ? systemColorScheme === 'dark'
    : themePreference === 'dark';

  // Select appropriate theme
  const theme = useMemo(() => {
    return isDark ? darkTheme : lightTheme;
  }, [isDark]);

  const toggleTheme = () => {
    setThemePreference(prev => {
      if (prev === 'system') return 'dark';
      if (prev === 'dark') return 'light';
      return 'system';
    });
  };

  const value: ThemeContextType = {
    theme,
    themePreference,
    isDark,
    setTheme: setThemePreference,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

// Export both hook names for compatibility
export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

export function useAppTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useAppTheme must be used within a ThemeProvider');
  }
  return context;
}

export { ThemeContext };
export type { ThemePreference, AppTheme };