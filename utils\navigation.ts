import { router } from 'expo-router';
import { Platform } from 'react-native';

type AppRoutes = {
  home: () => void;
  wallet: () => void;
  community: () => void;
  rankings: () => void;
  profile: () => void;
  messenger: () => void;
  chat: (params: { 
    userId: string; 
    name: string; 
    avatar: string; 
    handle: string; 
    online: string 
  }) => void;
  selectFriend: () => void;
};

// Type-safe navigation functions
const AppRouter: AppRoutes = {
  home: () => router.navigate('/(tabs)'),
  wallet: () => router.navigate('/(tabs)/wallet'),
  community: () => router.navigate('/(tabs)/community'),
  rankings: () => router.navigate('/(tabs)/rankings'),
  profile: () => router.navigate('/(tabs)/profile'),
  messenger: () => router.navigate('/(tabs)/messenger'),
  chat: (params) => {
    router.push({
      pathname: '/chat',
      params
    });
  },
  selectFriend: () => {
    if (Platform.OS === 'web') {
      router.navigate('/select-friend');
    } else {
      router.push({
        pathname: '/(tabs)/select-friend',
        params: {}
      });
    }
  },
};

export { AppRouter };
