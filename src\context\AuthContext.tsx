import React from "react";

const AuthContext = React.createContext<any>(null);

// This hook can be used to access the user info.
export function useAuth() {
  return React.useContext(AuthContext);
}

export function AuthProvider(props: any) {
  const [user, setAuth] = React.useState<string | null>(null);

  return (
    <AuthContext.Provider
      value={{
        signIn: (email?: string, password?: string) => {
            // For testing, we'll use user=1, password=1
            if (email === '1' && password === '1') {
                setAuth("user-token-placeholder");
            }
        },
        signOut: () => {
          setAuth(null);
        },
        user,
      }}
    >
      {props.children}
    </AuthContext.Provider>
  );
}
