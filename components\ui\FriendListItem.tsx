import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { Friend } from '../../types/messaging';

interface FriendListItemProps {
  friend: Friend;
  onPress: (friend: Friend) => void;
}

const FriendListItem: React.FC<FriendListItemProps> = ({ friend, onPress }) => {
  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={() => onPress(friend)}
      activeOpacity={0.7}
    >
      <View style={styles.avatarContainer}>
        <Image source={{ uri: friend.avatar }} style={styles.avatar} />
        {friend.online && <View style={styles.onlineIndicator} />}
      </View>
      <View style={styles.content}>
        <View style={styles.nameRow}>
          <Text style={styles.name} numberOfLines={1}>
            {friend.name}
          </Text>
          {friend.lastSeen && !friend.online && (
            <Text style={styles.lastSeen}>
              {friend.lastSeen}
            </Text>
          )}
        </View>
        <View style={styles.handleRow}>
          <Text style={styles.handle} numberOfLines={1}>
            {friend.handle}
          </Text>
          {friend.mutualFriends !== undefined && (
            <Text style={styles.mutualFriends}>
              {friend.mutualFriends} mutual friend{friend.mutualFriends !== 1 ? 's' : ''}
            </Text>
          )}
        </View>
      </View>
      <View style={styles.arrowContainer}>
        <Text style={styles.arrow}>›</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#1A1A1A',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#1A1A1A',
  },
  onlineIndicator: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#00FF9D',
    borderWidth: 2,
    borderColor: '#000',
  },
  content: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 2,
  },
  name: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  lastSeen: {
    color: '#666666',
    fontSize: 12,
  },
  handleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  handle: {
    color: '#666666',
    fontSize: 14,
    marginRight: 8,
    flex: 1,
  },
  mutualFriends: {
    color: '#3EC1F9',
    fontSize: 12,
  },
  arrowContainer: {
    marginLeft: 8,
  },
  arrow: {
    color: '#666666',
    fontSize: 24,
    fontWeight: 'bold',
    lineHeight: 24,
  },
});

export default React.memo(FriendListItem);
