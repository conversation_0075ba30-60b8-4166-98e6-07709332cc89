import React, { useCallback, useEffect, useState } from 'react';
import { SafeAreaView, View, Text, StyleSheet, Image, TouchableOpacity, FlatList, Dimensions, RefreshControl } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Header from '../../components/Header';
import { LinearGradient } from 'expo-linear-gradient';
import { LoadingState } from '../../components/LoadingState';
import { useLoadingStates } from '../../hooks/useLoadingStates';

const { width } = Dimensions.get('window');

interface PerformanceData {
  '24h': number;
  '7d': number;
  '30d': number;
  '1y': number;
}

interface Trader {
  id: string;
  rank: number;
  name: string;
  handle: string;
  avatar: string;
  basePortfolioValue: number;
  portfolioValue: number;
  hidden: boolean;
  trophy?: string;
  change: number;
  performance: PerformanceData;
  formattedPortfolioValue: string;
  formattedChange: string;
}

const timeframes = ['24h', '7d', '30d', '1y'] as const;
type TimeframeType = typeof timeframes[number];

// List of realistic trader names and handles
const TRADER_NAMES = [
  { name: '<PERSON>', handle: '@cryptoalex' },
  { name: '<PERSON>', handle: '@sarahcrypto' },
  { name: '<PERSON> <PERSON>', handle: '@mikebtc' },
  { name: '<PERSON> <PERSON>', handle: '@jwongtrades' },
  { name: '<PERSON> Kim', handle: '@davidk' },
  { name: '<PERSON> <PERSON>', handle: '@ewi<PERSON>' },
  { name: '<PERSON> <PERSON>', handle: '@jamess' },
  { name: 'Olivia Brown', handle: '@oliviab' },
  { name: 'William Taylor', handle: '@wtaylor' },
  { name: 'Sophia Martinez', handle: '@smartinez' },
  { name: 'Benjamin Anderson', handle: '@banderson' },
  { name: 'Ava Thomas', handle: '@avathomas' },
  { name: 'Lucas Jackson', handle: '@lucasj' },
  { name: 'Mia White', handle: '@miaw' },
  { name: 'Henry Harris', handle: '@henryh' },
  { name: 'Charlotte Martin', handle: '@charlottem' },
  { name: 'Alexander Lee', handle: '@alexlee' },
  { name: 'Amelia Clark', handle: '@ameliac' },
  { name: 'Daniel Lewis', handle: '@daniell' },
  { name: 'Harper Walker', handle: '@harperw' },
  { name: 'Ethan Hall', handle: '@ethanh' },
  { name: 'Abigail Allen', handle: '@abia' },
  { name: 'Matthew Young', handle: '@mattyoung' },
  { name: 'Emily Hernandez', handle: '@emilyh' },
  { name: 'Logan King', handle: '@logank' },
  { name: 'Elizabeth Wright', handle: '@elizw' },
  { name: 'Jackson Scott', handle: '@jacksons' },
  { name: 'Ella Green', handle: '@ellag' },
  { name: 'Sebastian Adams', handle: '@seba' },
  { name: 'Scarlett Baker', handle: '@scarlb' },
];

// Generate performance data for different timeframes with MORE VARIATION
const generatePerformanceData = (baseValue: number): PerformanceData => {
  // Create more dramatic differences between timeframes to make ranking changes visible
  const randomMultipliers = {
    '24h': 0.7 + Math.random() * 0.6,   // 70% to 130% of base (wider range)
    '7d': 0.6 + Math.random() * 0.8,    // 60% to 140% of base  
    '30d': 0.5 + Math.random() * 1.0,   // 50% to 150% of base
    '1y': 0.4 + Math.random() * 1.2,    // 40% to 160% of base (most variation)
  };
  
  return {
    '24h': baseValue * randomMultipliers['24h'],
    '7d': baseValue * randomMultipliers['7d'],
    '30d': baseValue * randomMultipliers['30d'],
    '1y': baseValue * randomMultipliers['1y'],
  };
};

const generateMoreTraders = (startIndex: number, count: number): Trader[] => {
  const newTraders = Array.from({ length: count }, (_, i) => {
    const traderIndex = (startIndex + i) % TRADER_NAMES.length;
    const nameData = TRADER_NAMES[traderIndex];
    const uniqueSuffix = Math.floor((startIndex + i) / TRADER_NAMES.length);
    
    // Generate a base portfolio value that will be used for all timeframes
    const basePortfolioValue = 10000 + Math.random() * 900000; // $10k to $1M
    const performance = generatePerformanceData(basePortfolioValue);
    
    return {
      id: `t-${startIndex + i}`,
      rank: 0, // Will be set after sorting
      name: uniqueSuffix > 0 ? `${nameData.name} ${uniqueSuffix + 1}` : nameData.name,
      handle: uniqueSuffix > 0 ? `${nameData.handle}${uniqueSuffix + 1}` : nameData.handle,
      avatar: `https://i.pravatar.cc/150?img=${startIndex + i + 1}`,
      basePortfolioValue,
      portfolioValue: basePortfolioValue,
      hidden: Math.random() > 0.8,
      change: 0,
      performance,
      formattedPortfolioValue: '',
      formattedChange: ''
    };
  });

  // Sort by base portfolio value in descending order and assign ranks
  return newTraders
    .sort((a, b) => b.basePortfolioValue - a.basePortfolioValue)
    .map((trader, index) => ({
      ...trader,
      rank: index + 1,
      portfolioValue: trader.basePortfolioValue,
      change: ((trader.basePortfolioValue / trader.basePortfolioValue) - 1) * 100,
      formattedPortfolioValue: `$${trader.basePortfolioValue.toLocaleString('en-US', { 
        minimumFractionDigits: 2, 
        maximumFractionDigits: 2 
      })}`,
      formattedChange: `+${((trader.basePortfolioValue / trader.basePortfolioValue - 1) * 100).toFixed(2)}%`
    }));
};

export default function RankingsScreen() {
  const [selectedTimeframe, setSelectedTimeframe] = useState<TimeframeType>('1y');
  const [showHidden, setShowHidden] = useState(false);
  const [traders, setTraders] = useState<Trader[]>([]);
  const [baseTraders, setBaseTraders] = useState<Trader[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isTabLoading, setIsTabLoading] = useState(false);
  
  // Use standardized loading states
  const {
    isInitialLoading,
    isRefreshing,
    isLoadingMore,
    setIsInitialLoading,
    setIsRefreshing,
    setIsLoadingMore
  } = useLoadingStates();

  // Update traders when timeframe changes
  useEffect(() => {
    if (baseTraders.length === 0) return;
    
    const updateTradersForTimeframe = () => {
      // Create a deep copy of base traders and update values for selected timeframe
      const updatedTraders = baseTraders.map(trader => {
        const value = trader.performance[selectedTimeframe];
        const change = ((value / trader.basePortfolioValue) - 1) * 100;
        
        return {
          ...trader,
          portfolioValue: value,
          change: change,
          formattedPortfolioValue: `${value.toLocaleString('en-US', { 
            minimumFractionDigits: 2, 
            maximumFractionDigits: 2 
          })}`,
          formattedChange: `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`
        };
      });
      
      // CRITICAL: Sort by portfolio value in DESCENDING order (highest to lowest)
      // This ensures ranking is always decreasing in value
      updatedTraders.sort((a, b) => b.portfolioValue - a.portfolioValue);
      
      // Assign ranks based on the new sorted order (1 = highest value, 2 = second highest, etc.)
      const rankedTraders = updatedTraders.map((trader, index) => ({
        ...trader,
        rank: index + 1  // Rank 1 = highest portfolio value
      }));
      
      setTraders(rankedTraders);
    };
    
    updateTradersForTimeframe();
  }, [selectedTimeframe, baseTraders]);
  
  // Initial data load effect
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setError(null);
        
        if (baseTraders.length === 0) {
          setIsInitialLoading(true);
        } else {
          setIsTabLoading(true);
        }
        
        // Simulate network request for initial load
        await new Promise(resolve => setTimeout(resolve, 500));
        // Generate initial data
        const initialData = generateMoreTraders(0, 20);
        setBaseTraders(initialData);
      } catch (err) {
        setError('Failed to load rankings. Please try again.');
        console.error('Error loading rankings:', err);
      } finally {
        setIsInitialLoading(false);
        setIsTabLoading(false);
      }
    };
    
    loadInitialData();
  }, [setIsInitialLoading, baseTraders.length]);

  const loadMoreTraders = useCallback(async () => {
    if (isLoadingMore || isInitialLoading || isTabLoading) return;

    try {
      setIsLoadingMore(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      const newTraders = generateMoreTraders(baseTraders.length, 10);
      setBaseTraders(prev => [...prev, ...newTraders]);
    } catch (err) {
      setError('Failed to load more traders. Please try again.');
      console.error('Error loading more traders:', err);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, isInitialLoading, isTabLoading, baseTraders.length, setIsLoadingMore]);

  const onRefresh = useCallback(async () => {
    if (isRefreshing || isTabLoading) return;
    
    try {
      setError(null);
      setIsRefreshing(true);
      // Simulate network request
      await new Promise(resolve => setTimeout(resolve, 1000));
      const refreshedTraders = generateMoreTraders(0, 20);
      setBaseTraders(refreshedTraders);
      if (isInitialLoading) setIsInitialLoading(false);
    } catch (err) {
      setError('Failed to refresh rankings. Please try again.');
      console.error('Error refreshing rankings:', err);
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing, isInitialLoading, isTabLoading, setIsInitialLoading, setIsRefreshing]);

  const renderFooter = useCallback(() => {
    if (!isLoadingMore || isInitialLoading || isRefreshing) return null;
    return (
      <View style={styles.listFooter}>
        <LoadingState size={40} />
      </View>
    );
  }, [isLoadingMore, isInitialLoading, isRefreshing]);

  const renderTrader = useCallback(({ item }: { item: Trader }) => {
    // ALWAYS show trophy for top 3 ranks (gold, silver, bronze)
    const getTrophyEmoji = (rank: number) => {
      switch(rank) {
        case 1: return '🥇'; // Gold for #1
        case 2: return '🥈'; // Silver for #2  
        case 3: return '🥉'; // Bronze for #3
        default: return null;
      }
    };
    
    const trophyEmoji = getTrophyEmoji(item.rank);
    const isPositive = item.change >= 0;
    
    return (
      <View style={[
        styles.traderRow,
        item.rank <= 3 && styles.topThree, // Highlight top 3
      ]}>
        <View style={styles.rankColumn}>
          {trophyEmoji ? (
            <Text style={[styles.trophyText, { fontSize: item.rank === 1 ? 28 : 24 }]}>
              {trophyEmoji}
            </Text>
          ) : (
            <Text style={styles.rankText}>#{item.rank}</Text>
          )}
        </View>
        <View style={styles.traderColumn}>
          <Image source={{ uri: item.avatar }} style={styles.avatar} />
          <View style={styles.traderInfo}>
            <Text style={styles.traderName} numberOfLines={1} ellipsizeMode="tail">
              {item.name}
            </Text>
            <Text style={styles.traderHandle} numberOfLines={1}>
              {item.handle}
            </Text>
          </View>
        </View>
        <View style={styles.valueColumn}>
          <Text style={styles.valueText}>
            {item.hidden && !showHidden ? '****' : item.formattedPortfolioValue}
          </Text>
          <Text style={[
            styles.changeText,
            isPositive ? styles.positiveChange : styles.negativeChange
          ]}>
            {item.formattedChange}
          </Text>
        </View>
      </View>
    );
  }, [showHidden]);

  const handleTimeframeChange = useCallback((timeframe: TimeframeType) => {
    setSelectedTimeframe(timeframe);
  }, []);

  const ListHeader = useCallback(() => (
    <View style={styles.listHeader}>
      <View style={styles.timeframeContainer}>
        {timeframes.map((timeframe) => (
          <TouchableOpacity
            key={timeframe}
            style={[
              styles.timeframeButton,
              selectedTimeframe === timeframe && styles.selectedTimeframe,
            ]}
            onPress={() => handleTimeframeChange(timeframe)}
          >
            <Text style={[
              styles.timeframeText,
              selectedTimeframe === timeframe && styles.selectedTimeframeText,
            ]}>
              {timeframe}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  ), [selectedTimeframe, handleTimeframeChange]);

  // Full-screen loader for initial load
  if (isInitialLoading && traders.length === 0) {
    return (
      <SafeAreaView style={styles.safeAreaLoading}>
        <Header />
        <View style={styles.fullScreenLoaderContainer}>
          <LoadingState size={80} fullScreen />
        </View>
      </SafeAreaView>
    );
  }

  // Show error state if there was an error loading data
  if (error && traders.length === 0) {
    return (
      <SafeAreaView style={styles.safeAreaError}>
        <Header />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={onRefresh}
            disabled={isRefreshing}
          >
            <Text style={styles.retryButtonText}>
              {isRefreshing ? 'Loading...' : 'Try Again'}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Loader when refreshing an empty list (after initial load attempt)
  if (isRefreshing && traders.length === 0 && !isInitialLoading) {
    return (
      <SafeAreaView style={styles.safeAreaLoading}>
        <Header />
        <View style={styles.fullScreenLoaderContainer}>
          <LoadingState size={80} fullScreen />
          <Text style={[styles.loadingText, { color: '#fff' }]}>Refreshing rankings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <Header />
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.titleRow}>
            <Text style={styles.title}>Portfolio Rankings</Text>
            <TouchableOpacity
              style={styles.showAllButton}
              onPress={() => setShowHidden(!showHidden)}
            >
              <Ionicons name={showHidden ? "eye-outline" : "eye-off-outline"} size={20} color="#fff" />
              <Text style={styles.showAllText}>
                {showHidden ? 'Hide Values' : 'Show All'}
              </Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.subtitle}>Top performing traders in your network</Text>
          
          {isTabLoading && (
            <View style={styles.tabLoadingContainer}>
              <LoadingState size={40} />
            </View>
          )}

          <FlatList
            data={traders}
            renderItem={renderTrader}
            keyExtractor={(item: Trader) => item.id}
            ListHeaderComponent={ListHeader}
            ListFooterComponent={renderFooter}
            ListEmptyComponent={
              !isInitialLoading && !isRefreshing && traders.length === 0 ? (
                <View style={styles.emptyListContainer}>
                  <Text style={styles.emptyListText}>No traders found. Pull to refresh.</Text>
                </View>
              ) : null
            }
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            onEndReached={loadMoreTraders}
            onEndReachedThreshold={0.5}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={onRefresh}
                colors={['#3EC1F9']}
                tintColor="#3EC1F9"
              />
            }
          />
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  // Base containers
  safeArea: {
    flex: 1,
    backgroundColor: '#000',
  },
  safeAreaLoading: {
    flex: 1,
    backgroundColor: '#000',
  },
  safeAreaError: {
    flex: 1,
    backgroundColor: '#000',
  },
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  content: {
    flex: 1,
  },
  
  // Header section
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  subtitle: {
    fontSize: 16,
    color: '#888',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  showAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4,
  },
  showAllText: {
    marginLeft: 4,
    color: '#fff',
    fontWeight: '500',
  },
  
  // List components
  listContent: {
    paddingBottom: 20,
  },
  listHeader: {
    paddingHorizontal: 20,
    paddingTop: 0,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  listFooter: {
    padding: 16,
    alignItems: 'center',
  },
  
  // Timeframe selector
  timeframeContainer: {
    flexDirection: 'row',
    backgroundColor: '#1A1A1A',
    borderRadius: 25,
    padding: 4,
    marginBottom: 20,
  },
  timeframeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: 'center',
  },
  selectedTimeframe: {
    backgroundColor: '#3EC1F9',
  },
  timeframeText: {
    fontSize: 14,
    color: '#888',
    fontWeight: '600',
  },
  selectedTimeframeText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  
  // Trader row
  traderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(82, 82, 82, 0.7)', // Reduced alpha to make it less bright
  },
  topThree: {
    backgroundColor: 'rgba(62, 193, 249, 0.1)', // 10% alpha blue
  },
  
  // Rank column
  rankColumn: {
    width: 40,
    alignItems: 'center',
    marginRight: 12,
  },
  trophyText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  rankText: {
    fontSize: 16,
    color: '#666',
  },
  
  // Trader info
  traderColumn: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  traderInfo: {
    flex: 1,
  },
  traderName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
    color: '#fff',
  },
  traderHandle: {
    fontSize: 14,
    color: '#888',
  },
  
  // Value column
  valueColumn: {
    alignItems: 'flex-end',
  },
  valueText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    color: '#fff',
  },
  changeText: {
    fontSize: 14,
  },
  positiveChange: {
    color: '#4CAF50',
  },
  negativeChange: {
    color: '#F44336',
  },
  
  // Loading states
  fullScreenLoaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
  },
  
  // Empty states
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#3EC1F9',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#000',
    fontWeight: '600',
  },
  tabLoadingContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  emptyListText: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
    marginTop: 16,
  },
});