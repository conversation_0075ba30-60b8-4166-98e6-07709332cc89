export interface AppTheme {
  name: 'light' | 'dark';
  colors: {
    // Brand & Interaction
    primary: string;
    secondary: string;
    accent: string;

    // Backgrounds
    background: {
      primary: string;    // Main screen background
      secondary: string;  // Tab bars, headers
      tertiary: string;   // Cards, modals
    };
    
    // Text
    text: {
      primary: string;
      secondary: string;
      tertiary: string;
      disabled: string;
      inverse: string;
    };

    // Borders & Dividers
    border: string;
    
    // Semantic Colors
    success: string;
    error: string;
    warning: string;
    
    // Performance/Wallet Indicators
    positive: string;
    negative: string;
    neutral: string;

    // Component-Specific
    skeletonBackground: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  typography: {
    h1: { fontSize: number; fontWeight: string };
    body: { fontSize: number; fontWeight: string };
  };
  radii: {
    sm: number;
    md: number;
    lg: number;
    full: number;
  };
  shadows: Record<string, any>;
}

export type ThemePreference = 'light' | 'dark' | 'system';
