# HodlHub - Technical Documentation

## Project Overview
HodlHub is a React Native mobile application designed as a social platform for cryptocurrency enthusiasts. It allows users to connect, discuss market trends, share insights, and manage their crypto portfolio information. The application is built using Expo and leverages Supabase for backend services.

## Quick Start Guide

### Prerequisites
- Node.js (LTS version recommended)
- npm or yarn
- Expo CLI: `npm install -g expo-cli`
- Git

### Setup Instructions
1.  **Clone the repository:**
    ```bash
    git clone https://github.com/alexandrepohl/hodlhub-repository.git
    cd hodlhub-repository
    ```
2.  **Install dependencies:**
    ```bash
    npm install
    # OR
    yarn install
    ```
3.  **Configure Environment Variables:**
    *   Create a `.env` file in the root of the project.
    *   Add necessary environment variables for Supabase URL and public key. (Note: Specific variable names need to be confirmed by checking the codebase for how Supabase is initialized).
    ```env
    EXPO_PUBLIC_SUPABASE_URL=YOUR_SUPABASE_URL
    EXPO_PUBLIC_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
    ```
4.  **Run the application:**
    *   **For iOS:** `npm run ios` or `yarn ios` (Requires Xcode and a simulator or connected device)
    *   **For Android:** `npm run android` or `yarn android` (Requires Android Studio and an emulator or connected device)
    *   **For Web (Development):** `npm run web` or `yarn web`

## Architecture Overview
The application is built with React Native and Expo. Key architectural features include:
- **Navigation:** Uses Expo Router for declarative routing. It includes an authentication stack (`(auth)`) and a main tab-based navigation (`(tabs)`) for core features.
- **UI Components:** A set of custom components are located in the `/components` directory.
- **State Management:** Basic authentication state is managed via `AuthContext`. Further investigation is needed to determine if a global state management library (e.g., Redux, Zustand) is used for other application states.
- **Backend:** Supabase is used for backend services, likely including authentication, database, and storage.
- **Styling:** Styles are primarily managed using `StyleSheet.create` within components.

## Current Issues
A comprehensive list of identified technical debt, issues, risks, and recommendations can be found in the [Detailed Technical Debt Assessment](./TECHNICAL_DEBT.md).

Key areas include:
- Incomplete core features (e.g., placeholder authentication).
- Needs for code quality improvements (e.g., redundant components, inconsistent loading indicators).
- Gaps in TypeScript usage and error handling.
- Lack of a testing framework and test coverage.
- Opportunities for styling standardization and design system development.

## Development Workflow
1.  Ensure all prerequisites and setup instructions are completed.
2.  Create a new branch for your feature or bug fix: `git checkout -b feature/your-feature-name` or `fix/your-bug-fix`.
3.  Make your code changes.
4.  Test your changes on the target platforms (iOS, Android).
5.  Follow linting and formatting guidelines (details to be confirmed, e.g., Prettier config is present).
6.  Commit your changes with a clear and descriptive commit message.
7.  Push your branch and open a Pull Request for review.
```
