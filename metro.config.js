const { getDefaultConfig } = require('expo/metro-config');

// Get the default config
const config = getDefaultConfig(__dirname);

// Add support for SVG and other transformations
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve('react-native-svg-transformer'),
  assetPlugins: ['expo-asset/tools/hashAssetFiles'],
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

// Enhanced resolver for multiple file types
config.resolver = {
  ...config.resolver,
  // Support for various asset types
  assetExts: [
    ...config.resolver.assetExts.filter(ext => ext !== 'svg'), // Remove svg from assets
    'lottie', // Lottie animations
    'json',   // JSON files
    'png', 'jpg', 'jpeg', 'gif', 'webp', // Images
    'mp4', 'mov', 'avi', // Videos
    'mp3', 'wav', 'aac', // Audio
    'ttf', 'otf', 'woff', 'woff2', // Fonts
  ],
  // Source extensions (files that can be imported)
  sourceExts: [
    ...config.resolver.sourceExts, 
    'svg', // SVG as source files
    'ts', 'tsx', // TypeScript
    'jsx', // JSX
    'mjs', // ES modules
  ],
  // ✅ REMOVED: alias - let babel handle all the aliases to avoid conflicts
};

module.exports = config;