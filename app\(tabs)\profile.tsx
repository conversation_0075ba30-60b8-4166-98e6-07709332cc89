import React, { useState } from 'react';
import { 
  SafeAreaView, 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  Image, 
  Dimensions, 
  TouchableOpacity,
  StatusBar,
  Platform
} from 'react-native';
import { VictoryChart, VictoryLine, VictoryAxis, VictoryArea, VictoryScatter } from 'victory-native';
import { Defs, LinearGradient as SVGLinearGradient, Stop } from 'react-native-svg';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Stack } from 'expo-router';

const { width: screenWidth } = Dimensions.get('window');

// Type definitions for better TypeScript support
type TimeframeType = 'Daily' | 'Weekly' | 'Monthly' | 'Yearly';

interface ChartDataPoint {
  x: number;
  y: number;
}

interface UserInfo {
  name: string;
  username: string;
  bio: string;
  balance: string;
  changeAmount: string;
  changePercent: string;
  pnlColor: string;
  avatar: string;
}

interface StatData {
  label: string;
  value: string;
}

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
}

// Data configurations
const userInfo: UserInfo = {
  name: '<PERSON>',
  username: '@cryptoalex',
  bio: 'Crypto enthusiast | Technical Analysis | DeFi Explorer',
  balance: '$47,674,103',
  changeAmount: '$50,825',
  changePercent: '+11.89%',
  pnlColor: '#4CAF50',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
};

const chartDataConfig: Record<TimeframeType, ChartDataPoint[]> = {
  'Daily': [
    { x: 0, y: 32000 }, { x: 1, y: 35000 }, { x: 2, y: 38000 }, 
    { x: 3, y: 40000 }, { x: 4, y: 42000 }, { x: 5, y: 45000 }, { x: 6, y: 47000 }
  ],
  'Weekly': [
    { x: 0, y: 30000 }, { x: 1, y: 33000 }, { x: 2, y: 37000 }, 
    { x: 3, y: 39000 }, { x: 4, y: 42000 }, { x: 5, y: 45000 }, { x: 6, y: 47000 }
  ],
  'Monthly': [
    { x: 0, y: 28000 }, { x: 1, y: 32000 }, { x: 2, y: 36000 }, 
    { x: 3, y: 40000 }, { x: 4, y: 43000 }, { x: 5, y: 46000 }, { x: 6, y: 47000 }
  ],
  'Yearly': [
    { x: 0, y: 25000 }, { x: 1, y: 30000 }, { x: 2, y: 35000 }, 
    { x: 3, y: 38000 }, { x: 4, y: 42000 }, { x: 5, y: 45000 }, { x: 6, y: 47000 }
  ],
};

const statsData: StatData[] = [
  { label: 'Portfolio Value', value: '$125,000' },
  { label: 'Change', value: '+23.50%' },
  { label: 'Holdings', value: '234' },
  { label: 'Followers', value: '1.2K' },
];

const achievements: Achievement[] = [
  { 
    id: 'top-trader', 
    name: 'Top Trader', 
    description: 'Ranked in top 1% this month',
    icon: 'trophy', 
    color: '#FFD700' 
  },
  { 
    id: 'diamond-hands', 
    name: 'Diamond Hands', 
    description: 'Held through 30% dip',
    icon: 'diamond', 
    color: '#00D4FF' 
  },
  { 
    id: 'early-adopter', 
    name: 'Early Adopter', 
    description: 'Member since 2024',
    icon: 'rocket', 
    color: '#FF6B6B' 
  },
];

// Get safe status bar height for different platforms
const getStatusBarHeight = (): number => {
  if (Platform.OS === 'ios') {
    return 44; // iPhone safe area
  }
  return StatusBar.currentHeight || 24;
};

export default function ProfileScreen() {
  const [selectedTimeframe, setSelectedTimeframe] = useState<TimeframeType>('Yearly');
  const [selectedPoint, setSelectedPoint] = useState<ChartDataPoint | null>(null);
  
  const timeframes: TimeframeType[] = ['Daily', 'Weekly', 'Monthly', 'Yearly'];
  const currentChartData = chartDataConfig[selectedTimeframe];

  const formatCurrency = (value: number): string => {
    return `$${(value / 1000).toFixed(0)}k`;
  };

  const handleTimeframeChange = (timeframe: TimeframeType): void => {
    setSelectedTimeframe(timeframe);
    setSelectedPoint(null);
  };

  const handleChartPointPress = (datum: any): void => {
    setSelectedPoint({ x: datum.x, y: datum.y });
  };

  const handleBackPress = (): void => {
    // Add navigation logic here
    console.log('Back button pressed');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1E3A8A" translucent={false} />
      <Stack.Screen options={{ headerShown: false }} />
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        bounces={true}
        scrollEventThrottle={16}
      >
        {/* Header with Gradient Background */}
        <View style={styles.header}>
          <LinearGradient
            colors={['#1E3A8A', '#1E40AF', '#2563EB']}
            style={styles.headerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          />
          
          {/* Back button removed as per request */}

          <View style={styles.headerContent}>
            <Image 
              source={{ uri: userInfo.avatar }} 
              style={styles.avatar}
              resizeMode="cover"
            />
            <Text style={styles.userName}>{userInfo.name}</Text>
            <Text style={styles.userUsername}>{userInfo.username}</Text>
            <Text style={styles.userBio} numberOfLines={2}>
              {userInfo.bio}
            </Text>
          </View>
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          {/* Portfolio Value Section */}
          <View style={styles.portfolioSection}>
            <Text style={styles.portfolioValue}>{userInfo.balance}</Text>
            <View style={styles.changeContainer}>
              <Text style={styles.changeAmount}>{userInfo.changeAmount}</Text>
              <Text style={[styles.changePercent, { color: userInfo.pnlColor }]}>
                {userInfo.changePercent}
              </Text>
            </View>

            {/* Timeframe Selector */}
            <View style={styles.timeframeContainer}>
              {timeframes.map((timeframe) => (
                <TouchableOpacity
                  key={timeframe}
                  style={[
                    styles.timeframeButton,
                    selectedTimeframe === timeframe && styles.selectedTimeframeButton
                  ]}
                  onPress={() => handleTimeframeChange(timeframe)}
                  activeOpacity={0.7}
                >
                  <Text style={[
                    styles.timeframeText,
                    selectedTimeframe === timeframe && styles.selectedTimeframeText
                  ]}>
                    {timeframe}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Chart with Interactive Points */}
            <View style={styles.chartContainer}>
              {selectedPoint && (
                <View style={styles.tooltipContainer}>
                  <Text style={styles.tooltipText}>
                    {formatCurrency(selectedPoint.y)}
                  </Text>
                </View>
              )}
              
              <VictoryChart
                width={screenWidth - 40}
                height={220}
                padding={{ top: 30, bottom: 30, left: 60, right: 20 }}
                domain={{ y: [20000, 50000] }}
              >
                <Defs>
                  <SVGLinearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <Stop offset="0%" stopColor="#3EC1F9" stopOpacity="0.6"/>
                    <Stop offset="100%" stopColor="#3EC1F9" stopOpacity="0.1"/>
                  </SVGLinearGradient>
                </Defs>
                
                <VictoryAxis
                  style={{
                    axis: { stroke: 'transparent' },
                    ticks: { stroke: 'transparent' },
                    tickLabels: { fill: 'transparent' },
                  }}
                />
                <VictoryAxis
                  dependentAxis
                  tickFormat={(t: number) => `$${t/1000}k`}
                  style={{
                    axis: { stroke: 'transparent' },
                    ticks: { stroke: 'transparent' },
                    tickLabels: { fontSize: 12, padding: 5, fill: '#666' },
                    grid: { stroke: 'rgba(255, 255, 255, 0.05)', strokeWidth: 1 }
                  }}
                />
                
                <VictoryArea
                  data={currentChartData}
                  style={{
                    data: { 
                      fill: "url(#chartGradient)",
                      fillOpacity: 1,
                      stroke: "none"
                    }
                  }}
                  interpolation="monotoneX"
                />
                
                <VictoryLine
                  data={currentChartData}
                  style={{
                    data: { 
                      stroke: "#3EC1F9", 
                      strokeWidth: 3
                    }
                  }}
                  interpolation="monotoneX"
                />
                
                {/* Interactive scatter points */}
                <VictoryScatter
                  data={currentChartData}
                  size={6}
                  style={{
                    data: { 
                      fill: "transparent",
                      stroke: "transparent"
                    }
                  }}
                  events={[{
                    target: "data",
                    eventHandlers: {
                      onPressIn: () => {
                        return [{
                          target: "data",
                          mutation: (props: any) => {
                            handleChartPointPress(props.datum);
                            return null;
                          }
                        }];
                      }
                    }
                  }]}
                />
                
                {/* Show selected point */}
                {selectedPoint && (
                  <VictoryScatter
                    data={[selectedPoint]}
                    size={8}
                    style={{
                      data: { 
                        fill: "#3EC1F9",
                        stroke: "#fff",
                        strokeWidth: 2
                      }
                    }}
                  />
                )}
              </VictoryChart>
            </View>
          </View>

          {/* Stats Row */}
          <View style={styles.statsContainer}>
            {statsData.map((stat, index) => (
              <View key={`stat-${index}`} style={styles.statItem}>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </View>
            ))}
          </View>

          {/* Achievements Section */}
          <View style={styles.achievementsSection}>
            <Text style={styles.sectionTitle}>Achievements</Text>
            {achievements.map((achievement) => (
              <TouchableOpacity
                key={achievement.id} 
                style={styles.achievementItem}
                activeOpacity={0.8}
                onPress={() => console.log(`Achievement pressed: ${achievement.name}`)}
              >
                <View style={[styles.achievementIcon, { backgroundColor: `${achievement.color}20` }]}>
                  <Ionicons name={achievement.icon} size={24} color={achievement.color} />
                </View>
                <View style={styles.achievementContent}>
                  <Text style={styles.achievementName}>{achievement.name}</Text>
                  <Text style={styles.achievementDescription}>{achievement.description}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* Bottom Home Indicator */}
          <View style={styles.homeIndicator} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  header: {
    height: 300,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: getStatusBarHeight(),
    paddingHorizontal: 20,
    position: 'relative',
  },
  headerGradient: {
    ...StyleSheet.absoluteFillObject,
  },
  headerContent: {
    alignItems: 'center',
    zIndex: 1,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: '#fff',
    marginBottom: 16,
  },
  userName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  userUsername: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8,
  },
  userBio: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    paddingHorizontal: 40,
    lineHeight: 20,
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#000',
    paddingTop: 30,
  },
  portfolioSection: {
    paddingHorizontal: 20,
    marginBottom: 40,
  },
  portfolioValue: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 8,
  },
  changeContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
    gap: 8,
  },
  changeAmount: {
    fontSize: 16,
    color: '#888',
  },
  changePercent: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  timeframeContainer: {
    flexDirection: 'row',
    backgroundColor: '#1A1A1A',
    borderRadius: 25,
    padding: 4,
    marginBottom: 20,
  },
  timeframeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: 'center',
  },
  selectedTimeframeButton: {
    backgroundColor: '#3EC1F9',
  },
  timeframeText: {
    fontSize: 14,
    color: '#888',
    fontWeight: '600',
  },
  selectedTimeframeText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  chartContainer: {
    backgroundColor: '#0A1929',
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
  },
  tooltipContainer: {
    position: 'absolute',
    top: 10,
    right: 20,
    backgroundColor: 'rgba(62, 193, 249, 0.9)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    zIndex: 10,
  },
  tooltipText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 40,
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    position: 'relative',
    flex: 1,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#888',
    textAlign: 'center',
  },
  // Removed statUnderline style as it's no longer needed
  achievementsSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 20,
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  achievementIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  achievementContent: {
    flex: 1,
  },
  achievementName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  achievementDescription: {
    fontSize: 14,
    color: '#888',
    lineHeight: 18,
  },
});