import { AppTheme } from './types';

export const lightTheme: AppTheme = {
  name: 'light',
  colors: {
    primary: '#3EC1F9',
    secondary: '#5A9BD5',
    accent: '#FFC000',
    background: {
      primary: '#F0F2F5',
      secondary: '#FFFFFF',
      tertiary: '#FFFFFF',
    },
    text: {
      primary: '#1C1C1E',
      secondary: '#6E6E73',
      tertiary: '#C7C7CC',
      disabled: '#AEAEB2',
      inverse: '#FFFFFF',
    },
    border: '#D1D1D6',
    success: '#34C759',
    error: '#FF3B30',
    warning: '#FF9500',
    positive: '#28A745',
    negative: '#DC3545',
    neutral: '#6E6E73',
    skeletonBackground: '#E1E1E6',
  },
  spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32 },
  typography: { 
    h1: { fontSize: 32, fontWeight: 'bold' }, 
    body: { fontSize: 16, fontWeight: 'normal' } 
  },
  radii: { sm: 4, md: 8, lg: 16, full: 999 },
  shadows: {},
};
