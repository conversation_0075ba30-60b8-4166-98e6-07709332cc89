import React, { memo, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { Community } from '../../types/community';

interface CommunityItemProps {
  item: Community;
  onJoinToggle: (communityId: string, isJoined: boolean) => void;
}

const COMMUNITY_CARD_HEIGHT = 120;

export const CommunityItem = memo(({ item, onJoinToggle }: CommunityItemProps) => {
  const handleCommunityPress = useCallback(() => {
    router.push(`/community/${item.id}`);
  }, [item.id]);

  const handleJoinPress = useCallback((e: { stopPropagation: () => void }) => {
    e.stopPropagation();
    onJoinToggle(item.id, item.joined);
  }, [item.id, item.joined, onJoinToggle]);

  return (
    <TouchableOpacity
      style={styles.communityCard}
      onPress={handleCommunityPress}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={`${item.name} community, ${item.members} members`}
      accessibilityRole="button"
    >
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: item.image }}
          style={styles.communityImage}
          resizeMode="cover"
          accessibilityIgnoresInvertColors
        />
      </View>
      
      <View style={styles.communityInfo}>
        <Text style={styles.communityName} numberOfLines={1}>
          {item.name}
        </Text>
        <Text style={styles.communityDescription} numberOfLines={2}>
          {item.description}
        </Text>
        <View style={styles.membersContainer}>
          <Ionicons name="people" size={14} color="#666" />
          <Text style={styles.membersText}>
            {item.members.toLocaleString()} members
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.actionButton, item.joined && styles.joinedButton]}
        onPress={handleJoinPress}
        activeOpacity={0.8}
        accessible={true}
        accessibilityLabel={item.joined ? `Leave ${item.name}` : `Join ${item.name}`}
        accessibilityRole="button"
      >
        <Text style={[
          styles.actionButtonText,
          item.joined && styles.joinedButtonText
        ]}>
          {item.joined ? 'Leave' : 'Join'}
        </Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );
});

CommunityItem.displayName = 'CommunityItem';

const styles = StyleSheet.create({
  communityCard: {
    flexDirection: 'row',
    backgroundColor: '#1A1A1A',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#333',
    minHeight: COMMUNITY_CARD_HEIGHT,
  },
  imageContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#333',
    overflow: 'hidden',
  },
  communityImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  communityInfo: {
    flex: 1,
    marginLeft: 16,
    paddingRight: 8,
  },
  communityName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  communityDescription: {
    color: '#666',
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 6,
  },
  membersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  membersText: {
    color: '#666',
    fontSize: 14,
    marginLeft: 6,
    fontWeight: '400',
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3EC1F9',
    minWidth: 64,
    alignItems: 'center',
    justifyContent: 'center',
  },
  joinedButton: {
    backgroundColor: '#3EC1F9',
    borderColor: '#3EC1F9',
  },
  actionButtonText: {
    color: '#3EC1F9',
    fontWeight: '600',
    fontSize: 14,
  },
  joinedButtonText: {
    color: '#fff',
  },
});
