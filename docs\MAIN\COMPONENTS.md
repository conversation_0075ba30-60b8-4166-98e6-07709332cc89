# Component Library Audit

## Existing Components

### 1. `Header.tsx`
-   **Purpose**: Displays the main application header, including the logo, a link to the home screen, a chat/messenger icon, and a top-right menu for user actions.
-   **Props**: None.
-   **Usage**:
    ```tsx
    import Header from '@components/Header';

    // In a screen component:
    <Header />
    ```
-   **Notes**:
    -   Uses `@react-native-masked-view/masked-view` and `expo-linear-gradient` for a gradient effect on the logo text (non-web platforms).
    -   Provides a fallback for the logo on the web.
    -   Includes navigation to `/messenger` (via `router.push`) and incorporates the `TopRightMenu` component.
    -   The logo links to `/(tabs)`.

### 2. `PaginationLoading.tsx`
-   **Purpose**: Displays a loading indicator, specifically an animated rotating whale image (`../assets/images/BIG.png`), typically used for indicating more items are being loaded in a list (pagination).
-   **Props**: None.
-   **Usage**:
    ```tsx
    import PaginationLoading from '@components/PaginationLoading';

    // In a list component when loading more items:
    <PaginationLoading />
    ```
-   **Notes**:
    -   Uses `react-native-reanimated` for the rotation animation (infinite repeat).
    -   The image path `../assets/images/BIG.png` is hardcoded.
    -   The animation spins at a duration of 1500ms per rotation.

### 3. `PostCard.tsx`
-   **Purpose**: Renders a single post item, typically for a social media feed. Displays user information (avatar, name, handle), post content, timestamp, an optional image, an optional performance indicator, and action buttons (comments, reposts, likes).
-   **Props**:
    -   `item: Post`: An object conforming to the `Post` type (defined in `../types/post.ts`), containing all data for the post. The `Post` type includes:
        -   `id: string`
        -   `user: { name: string, handle: string, avatar: ImageSourcePropType | string }`
        -   `timestamp: string`
        -   `content: string`
        -   `image?: string` (URI for the post image)
        -   `stats: { comments: number, reposts: number, likes: number }`
        -   `performanceIndicator?: string`
-   **Usage**:
    ```tsx
    import PostCard from '@components/PostCard';
    import { Post } from '../types/post'; // Adjust path as needed

    const samplePost: Post = {
      id: '1',
      user: {
        name: 'Test User',
        handle: '@testuser',
        avatar: 'https://example.com/avatar.png' // or require('../assets/images/avatar.png')
      },
      timestamp: '1h ago',
      content: 'This is a sample post.',
      stats: {
        comments: 10,
        reposts: 5,
        likes: 20
      }
      // image and performanceIndicator are optional
    };

    <PostCard item={samplePost} />
    ```
-   **Notes**:
    -   Uses `React.memo` for performance optimization.
    -   Conditionally renders user avatar based on whether `item.user.avatar` is a string URI or a local image resource (`ImageSourcePropType`).
    -   Conditionally renders a performance indicator (coloring it green if it starts with '+' and red otherwise) and post image.
    -   Action buttons use `Ionicons` and currently do not have `onPress` handlers implemented beyond being wrapped in `TouchableOpacity`.

### 4. `TopRightMenu.tsx`
-   **Purpose**: Displays a user avatar button in the header that, when pressed, opens a modal menu. The menu provides options for navigating to Profile, Settings, and logging out.
-   **Props**: None.
-   **Usage**:
    ```tsx
    import TopRightMenu from '@components/TopRightMenu';

    // Typically used within the Header component or a similar top bar.
    <TopRightMenu />
    ```
-   **Notes**:
    -   Uses a `Modal` component to display the menu overlay.
    -   Uses `expo-router` for navigation (`router.push` for Profile and Settings, `router.replace` for Logout to clear history).
    -   Uses `useAuth` context to call `signOut`.
    -   Currently uses mock user data (`mockUser: { name: 'Satoshi', avatar: require('../assets/images/icon.png') }`) for the avatar and name displayed in the menu. This should be replaced with actual user data from the authentication context.
    -   Menu items include Profile (`/(tabs)/profile`), Settings (`/settings`), and Logout.

### 5. `WhaleLoader.tsx`
-   **Purpose**: A universal loading component that displays a Lottie whale animation (`@assets/hodlhub-whale-loading/hodlhub-whale-loading.json`) with fallback support to `ActivityIndicator`. Designed for cross-platform compatibility, especially handling Lottie asset paths for web and native.
-   **Props**:
    -   `size?: number`: Size of the loader (width and height), defaults to `200`.
    -   `style?: StyleProp<ViewStyle>`: Custom styles for the container.
    -   `speed?: number`: Animation speed for Lottie, defaults to `1`.
    -   `color?: string`: Color for the fallback `ActivityIndicator`, defaults to `'#3EC1F9'`.
    -   `showFallback?: boolean`: If true, forces the display of the fallback `ActivityIndicator`, defaults to `false`.
-   **Usage**:
    ```tsx
    import WhaleLoader from '@components/WhaleLoader';

    <WhaleLoader size={100} speed={1.5} />
    ```
-   **Notes**:
    -   Uses `lottie-react-native` for the animation.
    -   Includes complex logic to modify Lottie JSON asset paths (`p` and `u` properties within the `assets` array of the Lottie JSON) to ensure the embedded images (like `img_0.png`) are correctly loaded via `require()` for bundling.
    -   Handles animation loading states (`isReady`, `hasError`) and provides fallbacks if Lottie fails or if `showFallback` is true.
    -   `React.memo` is used for optimization.
    -   Specifies web-specific renderer settings for Lottie (`renderer: 'svg'`).

### 6. `WhaleSpinner.tsx`
-   **Purpose**: Displays a spinning whale SVG animation as a loading indicator. It uses an embedded SVG string and provides a fallback to `ActivityIndicator` if SVG rendering or animation setup fails.
-   **Props**:
    -   `size?: number`: Size of the spinner (width and height), defaults to `80`.
    -   `speed?: number`: Duration in milliseconds for one full rotation (controls animation speed), defaults to `2000` (2 seconds). The rotation is counter-clockwise ('-360deg').
    -   `color?: string`: Color for the fallback `ActivityIndicator`, defaults to `'#3EC1F9'`.
-   **Usage**:
    ```tsx
    import WhaleSpinner from '@components/WhaleSpinner';

    <WhaleSpinner size={100} speed={1500} />
    ```
-   **Notes**:
    -   Uses `react-native-svg` with `SvgXml` to render an embedded SVG string.
    -   Uses the `Animated` API from React Native for the rotation animation. `useNativeDriver` is set to `true` for native and `false` for web.
    -   `React.memo` is used for optimization.
    -   The SVG content (a complex whale graphic) is hardcoded as a string within the component.
    -   Includes an `onError` handler for `SvgXml` to set an error state and display the fallback.

### 7. `WhaleSpinner copy.tsx`
-   **Purpose**: Appears to be an identical or near-identical duplicate of `WhaleSpinner.tsx`. Both files define a `WhaleSpinner` component with the same SVG string, props, and animation logic.
-   **Props**: Same as `WhaleSpinner.tsx`.
-   **Notes**: This file is redundant and should be removed to avoid confusion and maintain a single source of truth for the `WhaleSpinner` component. This will be noted in the "Code Duplication Analysis" section.

### Components in `/components/ui/`

#### 7.1. `ui/LoadingWhale.tsx`
-   **Purpose**: A wrapper component that uses `WhaleSpinner` to display a whale loading animation, with an option to show customizable loading text below the spinner.
-   **Props**:
    -   `size?: number`: Size of the loading indicator (passed to `WhaleSpinner`), defaults to `80`.
    -   `style?: StyleProp<ViewStyle>`: Custom styles for the container.
    -   `color?: string`: Color for loading text and fallback `ActivityIndicator` (passed to `WhaleSpinner`), defaults to `'#3EC1F9'`.
    -   `showText?: boolean`: Whether to show loading text, defaults to `false`.
    -   `text?: string`: Custom loading text, defaults to `'Loading...'`.
    -   `speed?: number`: Animation speed in ms for one rotation (passed as `duration` to `WhaleSpinner`), defaults to `2000`.
-   **Usage**:
    ```tsx
    import LoadingWhale from '@components/ui/LoadingWhale';

    <LoadingWhale size={100} showText={true} text="Fetching data..." />
    ```
-   **Notes**:
    -   This component abstracts `WhaleSpinner` and adds optional text functionality.
    -   `React.memo` is used.

#### 7.2. `ui/UniversalLoader.tsx`
-   **Purpose**: A general-purpose loading component that uses `WhaleSpinner` as its core animation. It aims to replace `ActivityIndicator` usage with a consistent whale animation. Provides size presets (`small`, `medium`, `large`) and style variants (`default`, `inline`, `overlay`, `button`) for different layout contexts.
-   **Props**:
    -   `size?: 'small' | 'medium' | 'large' | number`: Size preset or custom numerical size. Defaults to `'medium'`. (Resolves to small: 40, medium: 80, large: 120).
    -   `style?: StyleProp<ViewStyle>`: Custom styles for the main container.
    -   `color?: string`: Color for text and fallback `ActivityIndicator` (passed to `WhaleSpinner`), defaults to `'#3EC1F9'`.
    -   `showText?: boolean`: Whether to show loading text, defaults to `false`.
    -   `text?: string`: Custom loading text, defaults to `'Loading...'`.
    -   `speed?: number`: Animation speed in ms for one rotation (passed as `duration` to `WhaleSpinner`), defaults to `2000`.
    -   `forceFallback?: boolean`: If true, renders `ActivityIndicator` directly instead of `WhaleSpinner`. Defaults to `false`.
    -   `variant?: 'default' | 'inline' | 'overlay' | 'button'`: Style variant for different use cases, defaults to `'default'`.
-   **Usage**:
    ```tsx
    import UniversalLoader, { SmallLoader, OverlayLoader } from '@components/ui/UniversalLoader';

    // Default usage
    <UniversalLoader size="large" showText={true} />

    // Using exported convenience component
    <SmallLoader color="#FF0000" />
    <OverlayLoader /> // Fills parent and shows semi-transparent background
    ```
-   **Notes**:
    -   Provides several exported convenience wrappers: `SmallLoader`, `MediumLoader`, `LargeLoader`, `InlineLoader`, `OverlayLoader`, `ButtonLoader` which pre-configure some props.
    -   The `forceFallback` prop explicitly renders an `ActivityIndicator`. Otherwise, `WhaleSpinner` handles its own fallback if SVG rendering fails.
    -   `React.memo` is used.
    -   Variant styles adjust padding, positioning (for 'overlay'), and background.

## Code Duplication Analysis

-   **`WhaleSpinner copy.tsx` vs `WhaleSpinner.tsx`**:
    -   The file `components/WhaleSpinner copy.tsx` appears to be a direct duplicate or a slightly modified test version of `components/WhaleSpinner.tsx`. This is redundant and can lead to confusion or accidental usage of an outdated/incorrect component.
    -   **Recommendation**: Review `WhaleSpinner copy.tsx`, determine if it has any unique, valuable changes not present in `WhaleSpinner.tsx`. If not, it should be deleted. If it does, those changes should be merged into `WhaleSpinner.tsx` and the copy deleted.

-   **Loading Indicators (`WhaleLoader.tsx`, `WhaleSpinner.tsx`, `ui/LoadingWhale.tsx`, `ui/UniversalLoader.tsx`, `PaginationLoading.tsx`)**:
    -   There are multiple components related to loading animations:
        -   `WhaleLoader.tsx`: Lottie-based animation with fallback.
        -   `WhaleSpinner.tsx`: SVG-based animation with fallback.
        -   `PaginationLoading.tsx`: Specific spinning image for pagination.
        -   `ui/LoadingWhale.tsx`: Wraps `WhaleSpinner` to add text.
        -   `ui/UniversalLoader.tsx`: Wraps `WhaleSpinner` with more variants and size presets, and exports convenience components like `SmallLoader`, `OverlayLoader`.
    -   While `UniversalLoader` and `LoadingWhale` aim to standardize by wrapping `WhaleSpinner`, the presence of `WhaleLoader` (Lottie) and `PaginationLoading` (direct image animation) suggests an inconsistent approach or legacy components.
    -   The `WHALE_LOADER_IMPLEMENTATION.md` file suggests a specific focus on the Lottie version, which might be superseded by the `WhaleSpinner` SVG version.
    -   **Recommendation**:
        -   Standardize on one primary loading animation mechanism (likely `WhaleSpinner` due to its use in `UniversalLoader`).
        -   Refactor `PaginationLoading.tsx` to use the standardized `WhaleSpinner` or `UniversalLoader` for consistency, if the visual difference is not critical.
        -   Deprecate and remove `WhaleLoader.tsx` if `WhaleSpinner.tsx` is the preferred SVG-based solution. Update or remove `WHALE_LOADER_IMPLEMENTATION.md` accordingly.
        -   Ensure `UniversalLoader.tsx` becomes the primary way to use loading indicators throughout the app.

-   **Avatar Rendering in `TopRightMenu.tsx`**:
    -   `TopRightMenu.tsx` uses `mockUser.avatar` with `require('../assets/images/icon.png')`. A similar pattern might be used in `PostCard.tsx` for local avatar images (though `PostCard` also handles string URIs).
    -   **Recommendation**: If user avatars need to be consistently handled (local vs. remote), consider a dedicated `Avatar` component or utility function.

## Reusability Assessment

-   **High Reusability**:
    -   `ui/UniversalLoader.tsx` (and its wrappers `SmallLoader`, etc.): Designed for high reusability across the application wherever a loading indicator is needed.
    -   `WhaleSpinner.tsx`: Core SVG spinner, highly reusable if a direct, unstyled spinner is needed.
    -   `ui/LoadingWhale.tsx`: Good for cases where a whale spinner with text is consistently required.
    -   `PostCard.tsx`: Highly reusable for displaying any content that fits the post structure.
    -   `Header.tsx`: Reusable as the main application header, though it has some hardcoded navigation.
    -   `TopRightMenu.tsx`: Reusable within the header, but tied to specific navigation paths and mock user data.

-   **Moderate Reusability**:
    -   `PaginationLoading.tsx`: Specific to pagination, but its visual style might be unique. If standardized with `UniversalLoader`, its reusability becomes part of `UniversalLoader`.

-   **Low Reusability/Needs Refinement**:
    -   `WhaleLoader.tsx`: Potentially legacy or redundant given `WhaleSpinner` and `UniversalLoader`.
    -   `TopRightMenu.tsx`: Could be made more reusable by accepting menu items and user data as props rather than hardcoding them.

## Shared vs. Page-Specific Components

This mapping is based on initial analysis and conventions.

-   **Shared Components (intended for use across multiple screens/features)**:
    -   `Header.tsx`
    -   `PaginationLoading.tsx`
    -   `PostCard.tsx`
    -   `TopRightMenu.tsx`
    -   `WhaleLoader.tsx` (though potentially deprecated)
    -   `WhaleSpinner.tsx`
    -   `components/ui/LoadingWhale.tsx`
    -   `components/ui/UniversalLoader.tsx` (and its exported variants)

-   **Page-Specific Components**:
    -   At this stage, no components in the `/components` directory appear to be strictly page-specific. They are designed as general-purpose UI elements. Page-specific compositions of these shared components would reside within the screen files in the `/app` directory.

## Styling Approaches

-   **Primary Method**: `StyleSheet.create` is used in all analyzed components for defining styles. This is a standard and performant approach in React Native.
-   **Platform-Specific Styles**:
    -   `Header.tsx` uses `Platform.OS === 'ios' ? ... : ...` for `paddingTop`.
    -   `WhaleLoader.tsx` uses `Platform.OS === 'web'` checks for Lottie renderer settings and fallback logic.
    -   `WhaleSpinner.tsx` sets `useNativeDriver: Platform.OS !== 'web'` for its animation.
-   **Style Organization**: Styles are co-located within their respective component files.
-   **Hardcoded Values**: Some color values (e.g., `'#000'`, `'#fff'`, `'#3EC1F9'`, `'#1A1A1A'`) and font sizes are hardcoded directly in style definitions.
-   **Consistency**:
    -   There's a generally consistent use of dark themes (black backgrounds, light text).
    -   Iconography relies on `@expo/vector-icons` (specifically `Ionicons`).
-   **Potential Inconsistencies/Areas for Improvement**:
    -   **Color Palette**: While there's a dark theme, a clearly defined and consistently used color palette (theme colors, shades for text, borders, backgrounds) is not explicit. This can lead to variations over time.
    -   **Typography**: Consistent font sizes, weights, and line heights for different text elements (headings, body text, captions) are not explicitly defined globally.
    -   **Spacing**: Consistent spacing units (margins, paddings) are not explicitly defined.

## Design System Gaps

Based on the current components and styling:

-   **No Defined Color Palette**: The application lacks a formally defined color palette with named swatches (e.g., `primary`, `secondary`, `accent`, `textPrimary`, `textSecondary`, `border`, `background`). This leads to manual color value entry and potential inconsistencies.
-   **No Defined Typography Scale**: A typographic scale (H1, H2, Body, Caption, etc.) with predefined font sizes, weights, and line heights is missing.
-   **No Standardized Spacing System**: A spacing scale (e.g., 4px, 8px, 12px, 16px, 24px, 32px) for margins, paddings, and layout is not defined.
-   **No Theming Support**: The current styling approach does not easily support theming (e.g., light/dark modes, different brand themes) without significant conditional logic in each component.
-   **Iconography System**: While `Ionicons` are used, there's no central definition or wrapper around icons that could help manage icon sets, sizes, or default colors consistently.
-   **Form Elements**: While not extensively reviewed, a lack of standardized input fields, buttons (beyond simple `TouchableOpacity`), and other form controls is a common gap if not explicitly built. `PostCard` uses `TouchableOpacity` for actions, but a dedicated `Button` component with consistent styling and states (disabled, pressed) is usually part of a design system.
-   **Layout Components**: No dedicated layout components (e.g., `Grid`, `Stack`, `Box` with standardized spacing props) were observed.

## Standardization Needs

-   **Loading Indicators**:
    -   **Action**: Consolidate all loading indications to use `UniversalLoader` and its variants.
    -   **Reason**: Ensures visual consistency, reduces redundant code, and simplifies maintenance.
-   **Color Management**:
    -   **Action**: Define a global color palette (e.g., in a `theme.ts` or `constants.ts` file) and reference these named colors in `StyleSheet` definitions instead of hardcoding hex values.
    -   **Reason**: Improves consistency, makes theme changes easier, and reduces errors from typos in color codes.
-   **Typography**:
    -   **Action**: Define a typography scale with standard font sizes, weights, and families for different text roles (e.g., `heading1`, `body`, `caption`). Apply these styles consistently.
    -   **Reason**: Ensures visual hierarchy and readability across the application.
-   **Spacing**:
    -   **Action**: Establish a consistent spacing unit system (e.g., multiples of 4 or 8) to be used for margins, paddings, and component dimensions.
    -   **Reason**: Creates visual rhythm and makes layouts more predictable.
-   **Component Props**:
    -   **Action**: For components like `TopRightMenu.tsx`, make them more data-driven by accepting props for things like user data and menu items.
    -   **Reason**: Enhances reusability and decouples components from specific data sources or navigation paths.
-   **Error Handling in Components**:
    -   **Action**: Establish a pattern for how components should display errors or handle failed operations (e.g., a generic error message component, specific error states within components).
    -   **Reason**: Provides a consistent user experience for error conditions.
-   **File Naming and Organization**:
    -   **Action**: Ensure consistent naming conventions for files and directories. Resolve the redundancy of `WhaleSpinner copy.tsx`.
    -   **Reason**: Improves codebase clarity and maintainability.
