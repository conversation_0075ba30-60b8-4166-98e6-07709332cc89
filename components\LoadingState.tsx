import React from 'react';
import { View, StyleSheet } from 'react-native';
import LoadingWhale from './ui/LoadingWhale';

interface LoadingStateProps {
  size?: number;
  fullScreen?: boolean;
}

export const LoadingState: React.FC<LoadingStateProps> = ({ 
  size = 40, 
  fullScreen = false 
}) => (
  <View style={[styles.container, fullScreen && styles.fullScreen]}>
    <LoadingWhale size={size} />
  </View>
);

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullScreen: {
    flex: 1,
  },
});

export default LoadingState;
