# Supabase Database Implementation

## Overview
This document outlines the Supabase-based database architecture for the HodlHub Communities application, leveraging PostgreSQL with real-time capabilities and built-in authentication.

## Architecture Components

### 1. Database Schema (PostgreSQL 14+)

#### Tables
```sql
-- Enable necessary extensions
create extension if not exists "uuid-ossp";
create extension if not exists "pgcrypto";

-- Users (extends Supabase Auth)
create table public.profiles (
  id uuid references auth.users on delete cascade not null primary key,
  username text unique not null,
  full_name text,
  avatar_url text,
  portfolio_change numeric(5,2),
  is_online boolean default false,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  constraint username_length check (char_length(username) >= 3)
);

-- Enable Row Level Security
alter table public.profiles enable row level security;

-- Posts
type post_visibility as enum ('public', 'followers', 'private');

create table public.posts (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.profiles(id) on delete cascade not null,
  content text not null,
  title text,
  image_url text,
  performance_indicator text,
  visibility post_visibility default 'public',
  upvotes bigint default 0,
  downvotes bigint default 0,
  comment_count bigint default 0,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Comments
create table public.comments (
  id uuid default uuid_generate_v4() primary key,
  post_id uuid references public.posts(id) on delete cascade not null,
  user_id uuid references public.profiles(id) on delete cascade not null,
  content text not null,
  like_count bigint default 0,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Reactions (polymorphic)
create table public.reactions (
  id uuid default uuid_generate_v4() primary key,
  user_id uuid references public.profiles(id) on delete cascade not null,
  entity_type text not null, -- 'post' or 'comment'
  entity_id uuid not null,    -- post_id or comment_id
  type text not null,         -- 'like', 'upvote', 'downvote', etc.
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  unique(user_id, entity_type, entity_id, type)
);

-- User relationships
create table public.user_relationships (
  follower_id uuid references public.profiles(id) on delete cascade,
  following_id uuid references public.profiles(id) on delete cascade,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  primary key (follower_id, following_id),
  check (follower_id != following_id)
);

-- Messages
create table public.conversations (
  id uuid default uuid_generate_v4() primary key,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

create table public.conversation_participants (
  conversation_id uuid references public.conversations(id) on delete cascade,
  user_id uuid references public.profiles(id) on delete cascade,
  last_read_at timestamp with time zone,
  primary key (conversation_id, user_id)
);

create table public.messages (
  id uuid default uuid_generate_v4() primary key,
  conversation_id uuid references public.conversations(id) on delete cascade not null,
  sender_id uuid references public.profiles(id) on delete cascade not null,
  content text not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indexes
create index idx_posts_user_id on public.posts(user_id);
create index idx_posts_created_at on public.posts(created_at desc);
create index idx_comments_post_id on public.comments(post_id);
create index idx_reactions_entity on public.reactions(entity_type, entity_id);
create index idx_messages_conversation on public.messages(conversation_id, created_at);

-- Full-text search
create index idx_posts_content_search on public.posts using gin(to_tsvector('english', content));
```

### 2. Row Level Security (RLS) Policies

```sql
-- Profiles RLS
create policy "Public profiles are viewable by everyone."
  on profiles for select
  using ( true );

create policy "Users can update own profile."
  on profiles for update
  using ( auth.uid() = id );

-- Posts RLS
create policy "Public posts are viewable by everyone."
  on posts for select
  using ( visibility = 'public' );

create policy "Followers can view followers-only posts"
  on posts for select
  using (
    visibility = 'followers' and
    exists (
      select 1 from user_relationships
      where user_relationships.following_id = posts.user_id
      and user_relationships.follower_id = auth.uid()
    )
  );

create policy "Users can view their own posts"
  on posts for select
  using ( user_id = auth.uid() );

create policy "Users can create posts"
  on posts for insert
  with check ( user_id = auth.uid() );

create policy "Users can update own posts"
  on posts for update
  using ( user_id = auth.uid() );

create policy "Users can delete own posts"
  on posts for delete
  using ( user_id = auth.uid() );

-- Similar RLS policies for other tables...
```

### 3. Database Functions

```sql
-- Update updated_at timestamp
create or replace function public.handle_updated_at()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

-- Apply to all tables with updated_at
create trigger handle_posts_updated_at
  before update on public.posts
  for each row
  execute function public.handle_updated_at();

-- Function to get user feed
create or replace function public.get_user_feed(p_user_id uuid, p_limit int, p_offset int)
returns table (
  id uuid,
  user_id uuid,
  content text,
  title text,
  image_url text,
  performance_indicator text,
  upvotes bigint,
  downvotes bigint,
  comment_count bigint,
  created_at timestamptz,
  username text,
  avatar_url text,
  is_liked boolean
) as $$
begin
  return query
  select 
    p.*,
    pr.username,
    pr.avatar_url,
    exists (
      select 1 from public.reactions r 
      where r.entity_type = 'post' 
      and r.entity_id = p.id 
      and r.user_id = p_user_id 
      and r.type = 'like'
    ) as is_liked
  from public.posts p
  join public.profiles pr on p.user_id = pr.id
  where p.visibility = 'public'
     or (p.visibility = 'followers' and exists (
       select 1 from public.user_relationships ur 
       where ur.follower_id = p_user_id 
       and ur.following_id = p.user_id
     ))
     or p.user_id = p_user_id
  order by p.created_at desc
  limit p_limit
  offset p_offset;
end;
$$ language plpgsql security definer;
```

## Data Access Layer

### 1. Client-Side Implementation

```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  }
});

// Types
type Tables = {
  profiles: Database['public']['Tables']['profiles']['Row'];
  posts: Database['public']['Tables']['posts']['Row'];
  comments: Database['public']['Tables']['comments']['Row'];
};

type PostWithUser = Tables['posts'] & {
  profiles: Pick<Tables['profiles'], 'username' | 'avatar_url'>;
};

// Example: Fetch user feed
export async function getUserFeed(userId: string, page = 0, pageSize = 10) {
  const { data, error } = await supabase
    .rpc('get_user_feed', {
      p_user_id: userId,
      p_limit: pageSize,
      p_offset: page * pageSize
    });
    
  if (error) throw error;
  return data as PostWithUser[];
}

// Example: Create post
export async function createPost(post: {
  content: string;
  title?: string;
  imageUrl?: string;
  performanceIndicator?: string;
  visibility?: 'public' | 'followers' | 'private';
}) {
  const { data, error } = await supabase
    .from('posts')
    .insert([{
      ...post,
      image_url: post.imageUrl,
      performance_indicator: post.performanceIndicator,
      visibility: post.visibility || 'public'
    }])
    .select('*')
    .single();
    
  if (error) throw error;
  return data;
}

// Example: Subscribe to new posts
export function subscribeToNewPosts(callback: (payload: any) => void) {
  return supabase
    .channel('schema-db-changes')
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'posts',
        filter: 'visibility=eq.public'
      },
      callback
    )
    .subscribe();
}
```

### 2. Real-time Subscriptions

```typescript
// Subscribe to new posts in a community
const subscription = supabase
  .channel('public-posts')
  .on(
    'postgres_changes',
    {
      event: 'INSERT',
      schema: 'public',
      table: 'posts',
      filter: 'visibility=eq.public'
    },
    (payload) => {
      console.log('New post!', payload.new);
      // Update UI with new post
    }
  )
  .subscribe();

// Subscribe to new messages in a conversation
const messageSubscription = supabase
  .channel(`conversation-${conversationId}`)
  .on(
    'postgres_changes',
    {
      event: 'INSERT',
      schema: 'public',
      table: 'messages',
      filter: `conversation_id=eq.${conversationId}`
    },
    (payload) => {
      console.log('New message:', payload.new);
      // Update chat UI
    }
  )
  .subscribe();
```

## Authentication

Supabase Auth handles user authentication with multiple providers:

```typescript
// Sign up with email/password
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password',
  options: {
    data: {
      username: 'newuser',
      full_name: 'New User'
    }
  }
});

// Sign in with Google
const { data, error } = await supabase.auth.signInWithOAuth({
  provider: 'google',
  options: {
    queryParams: {
      access_type: 'offline',
      prompt: 'consent',
    },
  },
});

// Handle auth state changes
const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
  console.log('Auth event:', event, session);
  // Update auth state in your app
});

// Cleanup subscription
subscription.unsubscribe();
```

## Storage

Supabase Storage for user uploads:

```typescript
// Upload user avatar
const uploadAvatar = async (file: File, userId: string) => {
  const fileExt = file.name.split('.').pop();
  const fileName = `${userId}-${Math.random()}.${fileExt}`;
  
  const { data, error } = await supabase.storage
    .from('avatars')
    .upload(fileName, file, {
      cacheControl: '3600',
      upsert: true
    });
    
  if (error) throw error;
  
  // Get public URL
  const { data: { publicUrl } } = supabase.storage
    .from('avatars')
    .getPublicUrl(fileName);
    
  // Update user profile
  const { error: updateError } = await supabase
    .from('profiles')
    .update({ avatar_url: publicUrl })
    .eq('id', userId);
    
  if (updateError) throw updateError;
  
  return publicUrl;
};
```

## Performance Optimization

1. **Database Indexes**:
   - Added indexes on frequently queried columns
   - GIN index for full-text search

2. **Query Optimization**:
   - Used stored procedures for complex queries
   - Implemented pagination with cursor-based pagination

3. **Caching**:
   - Supabase Edge Functions with Cache-Control headers
   - Client-side caching with SWR or React Query

4. **Real-time Optimizations**:
   - Selective subscriptions to minimize bandwidth
   - Debounced updates for high-frequency events

## Security

1. **Row Level Security (RLS)**:
   - All tables have RLS enabled
   - Fine-grained access control policies

2. **Authentication**:
   - JWT-based auth with short-lived tokens
   - Secure password policies
   - Multi-factor authentication (MFA) support

3. **API Security**:
   - Rate limiting at the edge
   - CORS configuration
   - SQL injection prevention with parameterized queries

## Deployment

1. **Local Development**:
   ```bash
   # Start Supabase locally
   npx supabase start
   
   # Apply migrations
   npx supabase db push
   ```

2. **Production Deployment**:
   ```bash
   # Link project
   npx supabase link --project-ref your-project-ref
   
   # Deploy database changes
   npx supabase db push
   
   # Deploy Edge Functions
   npx supabase functions deploy your-function
   ```

3. **Migrations**:
   ```bash
   # Create new migration
   npx supabase migration new your_migration_name
   
   # Apply migrations
   npx supabase db push
   ```

## Monitoring

1. **Dashboard**:
   - Database health
   - API usage
   - Storage usage
   - Realtime connections

2. **Logs**:
   - SQL query logs
   - Authentication logs
   - Edge Function logs

3. **Alerts**:
   - Anomaly detection
   - Rate limiting alerts
   - Error tracking

## Cost Estimation

1. **Free Tier**:
   - Up to 500MB database
   - 1GB file storage
   - 50,000 monthly active users
   - 2GB bandwidth

2. **Paid Plans**:
   - Pro: $25/month
     - 8GB database
     - 100GB storage
     - 100,000 monthly active users
     - 50GB bandwidth
   
   - Enterprise: Custom pricing
     - Custom limits
     - Dedicated instances
     - Priority support

## Next Steps

1. Set up local development environment
2. Configure authentication providers
3. Implement database migrations
4. Set up monitoring and alerts
5. Plan for scaling
6. Implement backup strategy
7. Set up CI/CD pipeline

---
*Document Version: 1.0*
*Last Updated: 2024-06-14*
