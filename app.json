{"expo": {"name": "HodlHub", "slug": "ho<PERSON><PERSON>b", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "ho<PERSON><PERSON>b", "userInterfaceStyle": "dark", "splash": {"backgroundColor": "#000000"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.hodlhub.app"}, "android": {"adaptiveIcon": {"backgroundColor": "#000000"}, "package": "com.hodlhub.app"}, "web": {"bundler": "metro", "favicon": "./assets/images/favicon.png", "output": "static"}, "plugins": ["expo-router", "expo-font", "expo-asset"], "experiments": {"typedRoutes": true, "tsconfigPaths": true}}}