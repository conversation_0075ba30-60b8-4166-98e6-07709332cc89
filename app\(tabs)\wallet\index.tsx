import React from 'react';
import { SafeAreaView, View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Header from '../../../components/Header';
import MaskedView from '@react-native-masked-view/masked-view';
import { router } from 'expo-router';

const exchanges = [
  {
    id: 'binance',
    name: 'Binance',
    logo: require('../../../assets/images/BINANCE.png'),
    connected: false,
  },
  {
    id: 'metamask',
    name: 'MetaMask',
    logo: require('../../../assets/images/metamask (1).png'),
    connected: false,
  },
  {
    id: 'coinbase',
    name: 'Coinbase',
    logo: require('../../../assets/images/coinbase (2).png'),
    connected: false,
  },
  {
    id: 'kraken',
    name: 'Kraken',
    logo: require('../../../assets/images/KRAKEN.png'),
    connected: false,
  },
];

export default function WalletScreen() {
  const handleSync = (exchange: typeof exchanges[0]) => {
    router.push({
      pathname: '/wallet/connect',
      params: { 
        exchangeId: exchange.id,
        exchangeName: exchange.name 
      }
    });
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: styles.container.backgroundColor }}>
      <Header />
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <View style={styles.header}>
            <MaskedView
            style={styles.titleContainer}
            maskElement={
              <Text style={styles.title}>
                Manage All Your{'\n'}Crypto From{'\n'}One Place
              </Text>
            }
          >
            <LinearGradient
              colors={['#0047AB', '#00FF9D']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={StyleSheet.absoluteFill}
            />
          </MaskedView>

          <Text style={styles.subtitle}>
            Track all your assets in real-time by connecting your favorite exchanges and wallets.
          </Text>
        </View>

        <View style={styles.exchangesGrid}>
          {exchanges.map((exchange) => (
            <View key={exchange.id} style={styles.exchangeCard}>
              <Image 
                source={exchange.logo}
                style={styles.exchangeLogo}
              />
              <Text style={styles.exchangeName}>{exchange.name}</Text>
              <TouchableOpacity 
                style={styles.syncButton}
                onPress={() => handleSync(exchange)}
              >
                <Text style={styles.syncButtonText}>Sync →</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
  },
  header: {
    width: '100%',
    alignItems: 'center',
    paddingTop: 32,
    paddingHorizontal: 24,
    marginBottom: 48,
  },
  titleContainer: {
    height: 160,
    width: '100%',
    marginBottom: 24,
  },
  title: {
    fontSize: 42,
    fontWeight: 'bold',
    color: '#fff',
    lineHeight: 48,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.8,
    textAlign: 'center',
    maxWidth: 300,
  },
  exchangesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 16,
    paddingHorizontal: 16,
  },
  exchangeCard: {
    width: '45%',
    backgroundColor: '#1A1A1A',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  exchangeLogo: {
    width: 48,
    height: 48,
    marginBottom: 12,
    resizeMode: 'contain',
  },
  exchangeName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  syncButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#3EC1F9',
  },
  syncButtonText: {
    color: '#3EC1F9',
    fontWeight: '600',
  },
});