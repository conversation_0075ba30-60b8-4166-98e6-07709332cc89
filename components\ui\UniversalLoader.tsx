import React from 'react';
import { StyleSheet, View, StyleProp, ViewStyle, Text, ActivityIndicator } from 'react-native';
// import WhaleLoader from '../WhaleLoader'; // Removed old loader
import Whale<PERSON>pinner from './WhaleSpinner'; // Added new spinner

interface UniversalLoaderProps {
  /**
   * Size of the loader (small, medium, large, or custom number)
   */
  size?: 'small' | 'medium' | 'large' | number;
  /**
   * Custom styles for the container
   */
  style?: StyleProp<ViewStyle>;
  /**
   * Color theme for the loader (for text and fallback ActivityIndicator)
   */
  color?: string;
  /**
   * Whether to show loading text
   */
  showText?: boolean;
  /**
   * Custom loading text
   */
  text?: string;
  /**
   * Animation speed (duration in ms for one rotation)
   * @default 2000
   */
  speed?: number;
  /**
   * Force fallback to ActivityIndicator - This is now implicitly handled by WhaleSpinner's error state.
   * Keeping prop for API compatibility but it won't directly force WhaleSpinner's fallback.
   * WhaleSpinner will fallback on its own if SVG rendering fails.
   */
  forceFallback?: boolean;
  /**
   * Variant for different use cases
   */
  variant?: 'default' | 'inline' | 'overlay' | 'button';
}

/**
 * Universal Loading Component
 * 
 * Replaces all ActivityIndicator usage with consistent whale animation (now SVG-based)
 * Provides multiple size presets and variants for different contexts
 */
const UniversalLoader: React.FC<UniversalLoaderProps> = ({
  size = 'medium',
  style,
  color = '#3EC1F9',
  showText = false,
  text = 'Loading...',
  speed = 2000, // Default duration for WhaleSpinner
  forceFallback = false, // Kept for API, but WhaleSpinner handles its own error fallback
  variant = 'default',
}) => {
  // Convert size presets to numbers
  const getSize = (): number => {
    if (typeof size === 'number') return size;
    
    switch (size) {
      case 'small': return 40;
      case 'medium': return 80;
      case 'large': return 120;
      default: return 80;
    }
  };

  const loaderSize = getSize();
  const containerStyle = getVariantStyle(variant);

  // If forceFallback is true, we can explicitly render ActivityIndicator here,
  // otherwise, WhaleSpinner will handle its own errors.
  // Note: Added ActivityIndicator to the import from react-native
  if (forceFallback) {
    return (
      <View style={[styles.container, styles.defaultContainer, style, {width: loaderSize, height: loaderSize}]}>
        <ActivityIndicator
            size={loaderSize > 50 ? 'large' : 'small'}
            color={color}
        />
        {showText && (
            <Text style={[styles.loadingText, { color }]}>
            {text}
            </Text>
        )}
      </View>
    );
  }

  return (
    <View style={[styles.container, containerStyle, style]}>
      <WhaleSpinner
        size={loaderSize}
        color={color} // For WhaleSpinner's own fallback
        speed={speed}
        // WhaleSpinner handles its own SVG errors, so no direct showFallback prop needed from here
      />
      {showText && (
        <Text style={[styles.loadingText, { color }]}>
          {text}
        </Text>
      )}
    </View>
  );
};

/**
 * Get variant-specific styles
 */
const getVariantStyle = (variant: string): StyleProp<ViewStyle> => {
  switch (variant) {
    case 'inline':
      return styles.inlineContainer;
    case 'overlay':
      return styles.overlayContainer;
    case 'button':
      return styles.buttonContainer;
    default:
      return styles.defaultContainer;
  }
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  defaultContainer: {
    // flex: 1, // Allowing component to size itself or be sized by parent
    backgroundColor: 'transparent',
  },
  inlineContainer: {
    paddingVertical: 20,
    backgroundColor: 'transparent',
  },
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Adjusted overlay for better visibility
    zIndex: 1000,
  },
  buttonContainer: {
    // paddingHorizontal: 16, // These might be better handled by the parent button
    // paddingVertical: 8,
    backgroundColor: 'transparent',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default React.memo(UniversalLoader);

// Export convenience components for common use cases
// These should still work as they just pass props to UniversalLoader
export const SmallLoader: React.FC<Omit<UniversalLoaderProps, 'size'>> = (props) => (
  <UniversalLoader {...props} size="small" />
);

export const MediumLoader: React.FC<Omit<UniversalLoaderProps, 'size'>> = (props) => (
  <UniversalLoader {...props} size="medium" />
);

export const LargeLoader: React.FC<Omit<UniversalLoaderProps, 'size'>> = (props) => (
  <UniversalLoader {...props} size="large" />
);

export const InlineLoader: React.FC<Omit<UniversalLoaderProps, 'variant'>> = (props) => (
  <UniversalLoader {...props} variant="inline" />
);

export const OverlayLoader: React.FC<Omit<UniversalLoaderProps, 'variant'>> = (props) => (
  <UniversalLoader {...props} variant="overlay" />
);

export const ButtonLoader: React.FC<Omit<UniversalLoaderProps, 'variant' | 'size'>> = (props) => (
  // Ensure ButtonLoader uses a small size by default as per its original intent
  <UniversalLoader {...props} variant="button" size="small" />
);
