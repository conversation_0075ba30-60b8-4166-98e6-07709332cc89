import React from 'react';
import { Stack } from 'expo-router';

export default function CommunityLayout(): React.JSX.Element {
  return (
    <Stack 
      screenOptions={{ 
        headerShown: false,
        animation: 'default',
        gestureEnabled: true,
        contentStyle: { backgroundColor: '#000' }, // Ensure consistent background
      }}
    >
      <Stack.Screen 
        name="index" 
        options={{
          title: 'Communities',
          headerShown: false,
          animation: 'default',
        }}
      />
      <Stack.Screen 
        name="[id]" 
        options={{
          title: 'Community Details',
          headerShown: false,
          animation: 'default',
          gestureEnabled: true,
        }}
      />
      <Stack.Screen 
        name="create" 
        options={{
          presentation: 'modal',
          animation: 'slide_from_bottom',
          headerShown: false,
          gestureEnabled: true,
          gestureDirection: 'vertical',
          title: 'Create Community',
          animationDuration: 300, // Smoother animation
        }}
      />
    </Stack>
  );
}