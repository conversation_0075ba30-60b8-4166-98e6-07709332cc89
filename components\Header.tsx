import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { Link, router } from 'expo-router';
import MaskedView from '@react-native-masked-view/masked-view';
import { LinearGradient } from 'expo-linear-gradient';
import TopRightMenu from './TopRightMenu'; // Ensure this path is correct
import { Ionicons } from '@expo/vector-icons';

export default function Header() {
  return (
    <View style={styles.header}>
      <Link href="/(tabs)" asChild>
        <TouchableOpacity style={styles.logoTouchable}>
          {Platform.OS === 'web' ? ( // MaskedView might have issues on web, provide fallback
            <Text style={[styles.logo, styles.webLogoFallback]}>HodlHub</Text>
          ) : (
            <MaskedView
              style={styles.logoContainer}
              maskElement={<Text style={styles.logo}>HodlHub</Text>}
            >
              <LinearGradient
                colors={['#0047AB', '#00FF9D']} // Example gradient colors
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.gradient}
              />
            </MaskedView>
          )}
        </TouchableOpacity>
      </Link>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start' }}>
        <View style={{ marginRight: 15 }}>
          <TouchableOpacity onPress={() => router.push('/messenger')}>
          <Ionicons name="send-outline" size={28} color="#fff" />
         </TouchableOpacity>
        </View>
        <View>
          <TopRightMenu />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 15 : 15,
    paddingBottom: 8,
    backgroundColor: '#000',
    borderBottomWidth: 0,
    minHeight: 60, // Ensure minimum touch target height
  },
  logoTouchable: {
    // Allows the entire logo area to be pressable
  },
  logoContainer: {
    height: 30, // Adjust height as needed
    width: 120, // Adjust width as needed for "HodlHub"
  },
  logo: {
    fontSize: 24, // Prominent logo size
    fontWeight: 'bold',
    color: 'white', // This color is masked by the gradient
    letterSpacing: -0.5,
  },
  webLogoFallback: {
    // On web, if MaskedView isn't used, LinearGradient won't apply to text directly.
    // This style attempts to mimic the gradient with a single color or you might need a different web approach.
    // For simplicity, we'll use a primary color from the gradient.
    color: '#0047AB',
  },
  gradient: {
    flex: 1,
  },
});