import React, { useState, useEffect } from 'react';
import { SafeAreaView, View, Text, StyleSheet, Image, TouchableOpacity, FlatList } from 'react-native';
import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import LoadingWhale from '../../components/ui/LoadingWhale';
import useAppNavigation from '../../hooks/useAppNavigation';
import Header from '../../components/Header';

interface MessageUser {
  name: string;
  handle: string;
  avatar: string;
  online: boolean;
}

interface Message {
  id: string;
  user: MessageUser;
  lastMessage: string;
  time: string;
  unread: number;
}

// Renamed from 'messages' to 'staticMessages' to avoid conflict with state variable
const staticMessages: Message[] = [
  {
    id: '1',
    user: {
      name: '<PERSON>',
      handle: '@cryptoalex',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e',
      online: true,
    },
    lastMessage: 'Did you see the latest Bitcoin price?',
    time: '2m ago',
    unread: 2,
  },
  {
    id: '2',
    user: {
      name: '<PERSON>',
      handle: '@tradingpro',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80',
      online: false,
    },
    lastMessage: 'Thanks for the trading tip!',
    time: '1h ago',
    unread: 0,
  },
  {
    id: '3',
    user: {
      name: 'Michael Park',
      handle: '@cryptowhale',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d',
      online: true,
    },
    lastMessage: 'Let\'s discuss the new DeFi protocol',
    time: '3h ago',
    unread: 5,
  },
  {
    id: '4',
    user: {
      name: 'Emma Wilson',
      handle: '@emmatrades',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330',
      online: true,
    },
    lastMessage: 'Check out this new trading strategy',
    time: '5h ago',
    unread: 1,
  },
  {
    id: '5',
    user: {
      name: 'David Kim',
      handle: '@dkimcrypto',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e',
      online: false,
    },
    lastMessage: 'The market is looking bullish!',
    time: '1d ago',
    unread: 0,
  },
  {
    id: '6',
    user: {
      name: 'Lisa Chang',
      handle: '@lisac',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330',
      online: true,
    },
    lastMessage: 'Have you analyzed the new token?',
    time: '1d ago',
    unread: 3,
  },
  {
    id: '7',
    user: {
      name: 'James Wilson',
      handle: '@jwilson',
      avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d',
      online: false,
    },
    lastMessage: 'What do you think about ETH 2.0?',
    time: '2d ago',
    unread: 0,
  },
  {
    id: '8',
    user: {
      name: 'Maria Garcia',
      handle: '@mgarcia',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2',
      online: true,
    },
    lastMessage: 'The NFT market is heating up! 🔥',
    time: '2d ago',
    unread: 1,
  },
  {
    id: '9',
    user: {
      name: 'Robert Taylor',
      handle: '@rtaylor',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e',
      online: false,
    },
    lastMessage: 'Let\'s review the portfolio strategy',
    time: '3d ago',
    unread: 0,
  },
  {
    id: '10',
    user: {
      name: 'Anna Lee',
      handle: '@alee',
      avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9',
      online: true,
    },
    lastMessage: 'Did you join the new trading group?',
    time: '3d ago',
    unread: 2,
  },
  {
    id: '11',
    user: {
      name: 'Chris Martinez',
      handle: '@cmartinez',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e',
      online: true,
    },
    lastMessage: 'Technical analysis looks promising',
    time: '4d ago',
    unread: 0,
  },
  {
    id: '12',
    user: {
      name: 'Sophie Brown',
      handle: '@sbrown',
      avatar: 'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04',
      online: false,
    },
    lastMessage: 'Thoughts on the market dip?',
    time: '4d ago',
    unread: 0,
  },
  {
    id: '13',
    user: {
      name: 'Daniel Lee',
      handle: '@dlee',
      avatar: 'https://images.unsplash.com/photo-1492562080023-ab3db95bfbce',
      online: true,
    },
    lastMessage: 'New investment opportunity! 💎',
    time: '5d ago',
    unread: 4,
  },
  {
    id: '14',
    user: {
      name: 'Rachel Kim',
      handle: '@rkim',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330',
      online: false,
    },
    lastMessage: 'Check out this trading bot',
    time: '5d ago',
    unread: 0,
  },
  {
    id: '15',
    user: {
      name: 'Thomas Wang',
      handle: '@twang',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e',
      online: true,
    },
    lastMessage: 'Let\'s discuss market trends',
    time: '6d ago',
    unread: 1,
  },
  {
    id: '16',
    user: {
      name: 'Isabella Silva',
      handle: '@isilva',
      avatar: 'https://images.unsplash.com/photo-1531123897727-8f129e1688ce',
      online: false,
    },
    lastMessage: 'Have you seen the new DeFi yield?',
    time: '6d ago',
    unread: 0,
  },
  {
    id: '17',
    user: {
      name: 'Kevin Zhang',
      handle: '@kzhang',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e',
      online: true,
    },
    lastMessage: 'Interesting arbitrage opportunity',
    time: '1w ago',
    unread: 2,
  },
  {
    id: '18',
    user: {
      name: 'Laura Chen',
      handle: '@lchen',
      avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb',
      online: false,
    },
    lastMessage: 'Portfolio rebalancing time!',
    time: '1w ago',
    unread: 0,
  },
  {
    id: '19',
    user: {
      name: 'Ryan Park',
      handle: '@rpark',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e',
      online: true,
    },
    lastMessage: 'New token listing alert! 🚀',
    time: '1w ago',
    unread: 3,
  },
  {
    id: '20',
    user: {
      name: 'Michelle Wu',
      handle: '@mwu',
      avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9',
      online: false,
    },
    lastMessage: 'Let\'s catch up on market analysis',
    time: '1w ago',
    unread: 0,
  },
];

export default function InboxScreen() {
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [messagesData, setMessagesData] = useState<Message[]>([]);

  useEffect(() => {
    const fetchMessages = async () => {
      // Not strictly needed to set isInitialLoading to true here for a mount-only effect,
      // as it's already true. But good practice if dependencies were present.
      // if (!isInitialLoading) setIsInitialLoading(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setMessagesData(staticMessages); // Use the predefined static messages
      setIsInitialLoading(false);
    };

    fetchMessages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array ensures this runs once on mount

  const renderMessage = ({ item }: { item: Message }) => (
    <TouchableOpacity 
      style={styles.messageContainer}
      onPress={() => {
        navigate({
          name: 'chat',
          params: {
            userId: item.id,
            name: item.user.name,
            avatar: item.user.avatar,
            handle: item.user.handle,
            online: String(item.user.online)
          }
        });
      }}
    >
      <View style={styles.avatarContainer}>
        <Image source={{ uri: item.user.avatar }} style={styles.avatar} />
        {item.user.online && <View style={styles.onlineIndicator} />}
      </View>
      <View style={styles.messageContent}>
        <View style={styles.messageHeader}>
          <Text style={styles.userName}>{item.user.name}</Text>
          <Text style={styles.messageTime}>{item.time}</Text>
        </View>
        <View style={styles.messagePreview}>
          <Text style={styles.messageText} numberOfLines={1}>
            {item.lastMessage}
          </Text>
          {item.unread > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadText}>{item.unread}</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  // Full-screen loader for initial load
  if (isInitialLoading && messagesData.length === 0) { // messagesData.length check is slightly redundant due to logic flow but good for clarity
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: styles.container.backgroundColor }}>
        <Header />
        <View style={styles.fullScreenLoaderContainer}>
          <LoadingWhale size={80} />
        </View>
      </SafeAreaView>
    );
  }

  const { navigate } = useAppNavigation();

  const handleCreateNewMessage = () => {
    navigate('messenger/select-friend' as const);
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Messages</Text>
          <TouchableOpacity 
            onPress={handleCreateNewMessage}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="create-outline" size={24} color="#3EC1F9" />
          </TouchableOpacity>
        </View>

        <FlatList
          data={messagesData}
          renderItem={renderMessage}
          keyExtractor={(item: Message) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={[
            messagesData.length === 0 && !isInitialLoading ? styles.emptyListContentContainer : null,
            { paddingBottom: 20 } // Add some bottom padding to the list
          ]}
          ListEmptyComponent={
            !isInitialLoading && messagesData.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>No messages yet.</Text>
              </View>
            ) : null
          }
        />
      </View>
      
      {/* Bottom Tab Navigation */}
      <View style={styles.tabBarContainer}>
        <TouchableOpacity 
          style={styles.tabItem}
          onPress={() => navigate('home')}
        >
          <Ionicons name="home-outline" size={24} color="#888888" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.tabItem}
          onPress={() => navigate('wallet')}
        >
          <Ionicons name="wallet-outline" size={24} color="#888888" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.tabItem}
          onPress={() => navigate('community')}
        >
          <Ionicons name="people-outline" size={24} color="#888888" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.tabItem}
          onPress={() => navigate('rankings')}
        >
          <Ionicons name="stats-chart-outline" size={24} color="#888888" />
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.tabItem}
          onPress={() => navigate('profile')}
        >
          <Ionicons name="person-circle-outline" size={24} color="#3EC1F9" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  content: {
    flex: 1,
    paddingTop: 50, // Increased padding to account for status bar and header spacing
    paddingBottom: 70, // Add padding to prevent content from being hidden behind tab bar
  },
  tabBarContainer: {
    flexDirection: 'row',
    height: 70, // Increased from 60 to 70
    backgroundColor: '#000000',
    borderTopWidth: 1,
    borderTopColor: '#222222',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingTop: 6, // Slightly reduced from 8 to move icons up a bit
  },
  tabItem: {
    flex: 1,
    justifyContent: 'flex-start', // Align icons to the top of the tab
    alignItems: 'center',
    paddingTop: 4, // Add small padding at the top
  },
  fullScreenLoaderContainer: { // Added for the loader
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000', // Match screen background
  },
  emptyContainer: { // For ListEmptyComponent
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: { // For ListEmptyComponent
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
  emptyListContentContainer: { // Style for FlatList when empty to center the emptyContainer
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 24,
    marginTop: 8, // Add some top margin to the title
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
  },
  messageContainer: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#1A1A1A',
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  onlineIndicator: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#00FF9D',
    borderWidth: 2,
    borderColor: '#000',
  },
  messageContent: {
    flex: 1,
    marginLeft: 12,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  messageTime: {
    color: '#666',
    fontSize: 12,
  },
  messagePreview: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  messageText: {
    color: '#666',
    fontSize: 14,
    flex: 1,
  },
  unreadBadge: {
    backgroundColor: '#0047AB',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  unreadText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});