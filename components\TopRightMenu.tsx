import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, Image, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '../src/context/AuthContext';
// import { LinearGradient } from 'expo-linear-gradient'; // If needed for styling menu items

// Mock user data, replace with actual data source if available
const mockUser = {
  name: '<PERSON>',
  avatar: { uri: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400' }, // Using the same mock photo as profile page
};

export default function TopRightMenu() {
  const [menuVisible, setMenuVisible] = useState(false);

  const handleNavigation = (path: string) => {
    setMenuVisible(false);
    router.push(path as any);
  };

  const { signOut } = useAuth();

  const handleLogout = () => {
    setMenuVisible(false);
    // Sign out the user
    signOut();
    // Navigate to login screen and clear navigation history
    router.replace({
      pathname: '/(auth)/login',
      params: { logout: 'true' }
    });
  };

  return (
    <View>
      <TouchableOpacity onPress={() => setMenuVisible(true)} style={styles.avatarButton}>
        <Image source={mockUser.avatar} style={styles.avatarImage} />
      </TouchableOpacity>

      <Modal
        animationType="fade"
        transparent={true}
        visible={menuVisible}
        onRequestClose={() => setMenuVisible(false)}
      >
        <TouchableOpacity style={styles.modalOverlay} onPress={() => setMenuVisible(false)} activeOpacity={1}>
          <View style={styles.menuContainer}>
            <View style={styles.menuHeader}>
              <Image source={mockUser.avatar} style={styles.menuAvatarImage} />
              <Text style={styles.menuUserName}>{mockUser.name}</Text>
            </View>

            <TouchableOpacity style={styles.menuItem} onPress={() => handleNavigation('/(tabs)/profile')}>
              <Ionicons name="person-circle-outline" size={22} color="#fff" style={styles.menuIcon} />
              <Text style={styles.menuItemText}>Profile</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.menuItem} onPress={() => handleNavigation('/settings')}>
              <Ionicons name="settings-outline" size={22} color="#fff" style={styles.menuIcon} />
              <Text style={styles.menuItemText}>Settings</Text>
            </TouchableOpacity>

            <View style={styles.menuDivider} />

            <TouchableOpacity style={styles.menuItem} onPress={handleLogout}>
              <Ionicons name="log-out-outline" size={22} color="#FF6B6B" style={styles.menuIcon} />
              <Text style={[styles.menuItemText, { color: '#FF6B6B' }]}>Logout</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  avatarButton: {
    padding: 5, // For easier touch
  },
  avatarImage: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderColor: '#3EC1F9',
    borderWidth: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'flex-start', // Align menu to top
    alignItems: 'flex-end', // Align menu to right
  },
  menuContainer: {
    backgroundColor: '#1A1A1A', // Dark background for the menu
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginTop: 60, // Adjust as needed to position below the header
    marginRight: 10,
    width: 220, // Fixed width for the menu
    // Replaced deprecated shadow props with boxShadow for web compatibility
    boxShadow: '0px 4px 5px rgba(0, 0, 0, 0.3)',
    elevation: 10,
  },
  menuHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 10,
    marginBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  menuAvatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  menuUserName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  menuIcon: {
    marginRight: 12,
  },
  menuItemText: {
    color: '#fff',
    fontSize: 16,
  },
  menuDivider: {
    height: 1,
    backgroundColor: '#333',
    marginVertical: 8,
  },
});
