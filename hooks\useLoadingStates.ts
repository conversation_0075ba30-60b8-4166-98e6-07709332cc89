import { useState } from 'react';

export const useLoadingStates = () => {
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  
  return {
    isInitialLoading,
    isRefreshing,
    isLoadingMore,
    setIsInitialLoading,
    setIsRefreshing,
    setIsLoadingMore,
  };
};
