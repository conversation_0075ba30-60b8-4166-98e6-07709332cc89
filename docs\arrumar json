Role: You are a Senior Staff Engineer assigned to Project hodlhub-communities-fix.

Mission: Your objective is to perform a comprehensive audit and remediation of the project's package.json file and its associated dependencies. You will eliminate dependency drift, resolve version conflicts, and ensure a stable, deterministic, and up-to-date build environment. This action is critical to remediate technical debt and ensure future scalability and developer onboarding efficiency.

Execution Plan: Dependency Audit & Remediation
Execute the following phases in sequence. Do not proceed to the next phase until the current one is successfully completed and verified.

Phase 1: Initial Diagnosis & Triage
Before making any changes, we will diagnose the current state of the project's health using Expo's native tooling.

Execute Diagnostic Tool: Run the expo-doctor utility to perform an automated check for known issues like dependency mismatches, broken configurations, and missing packages.

BASH

npx expo-doctor
Analyze & Remediate:

If expo-doctor reports errors or warnings: Prioritize resolving these issues first. Execute any commands recommended by the tool (e.g., npm install <missing-package>).
If expo-doctor reports no issues: Proceed to the next phase.
Verification: Run npx expo-doctor one final time to confirm a clean bill of health before continuing.

Phase 2: Dependency State Audit
Now, we will generate a detailed report of all outdated dependencies to inform our update strategy.

Execute Outdated Check: Run the npm outdated command to create a manifest of packages that have newer versions available.

BASH

npm outdated
Analyze the Report: Pay critical attention to the Wanted and Latest columns.

Wanted: Represents the latest version that satisfies the semantic versioning (^, ~) range in package.json. These are generally safe, non-breaking updates.
Latest: Represents the absolute latest version available, which may include breaking changes (major version bumps).
Phase 3: Strategic Dependency Upgrade
We will now execute a two-stage upgrade process, prioritizing stability over bleeding-edge versions.

Execute Safe Upgrades (Patch & Minor):

Action: Run the npm update command. This will upgrade all packages to their latest Wanted versions, respecting the existing semantic versioning rules. This is the safest first pass.
Command:
BASH

npm update
Execute Major Upgrades (Calculated Risk):

Identify Critical Packages: From the npm outdated report, identify major version updates for critical ecosystem packages, including but not limited to:
expo
react
react-native
expo-router
Action: For each critical package identified, update it to its @latest version individually. This allows us to control the process and troubleshoot potential breaking changes one at a time.
Command (Example):
BASH

npm install expo@latest expo-router@latest
Phase 4: Final Sanitization & Deterministic Install
The package.json is now updated. We must ensure the node_modules directory perfectly reflects this new source of truth.

Purge Local State: Delete the node_modules directory and the package-lock.json file. This is non-negotiable and guarantees that the next install is a completely fresh, deterministic build based only on the updated package.json.

BASH

# Use appropriate commands for the OS. For PowerShell:
Remove-Item -Recurse -Force node_modules
Remove-Item -Force package-lock.json
Execute Clean Installation: Run npm install to create a new node_modules directory and a fresh package-lock.json file from the updated package.json.

BASH

npm install
Definition of Done (Validation Criteria)
Your mission is complete ONLY when the following criteria are met:

npx expo-doctor runs and reports zero issues.
npm outdated shows no, or a significantly reduced, list of outdated packages (major version differences may remain by design).
The project successfully starts without dependency-related errors by running npx expo start --clear.