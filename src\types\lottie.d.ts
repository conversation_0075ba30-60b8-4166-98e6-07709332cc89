declare module '*.json' {
  import { LottieAnimationData } from 'lottie-react-native';
  const value: LottieAnimationData;
  export default value;
}

declare module '*.png' {
  const value: number | { uri: string };
  export default value;
}

declare module '@assets/*' {
  const value: any;
  export default value;
}

// Lottie types for better type safety
interface LottieAsset {
  id: string;
  w?: number;
  h?: number;
  u?: string;
  p?: string | number | { uri: string };
  e?: number;
  [key: string]: any;
}

interface LottieLayer {
  ty: number;
  nm: string;
  ind: number;
  refId?: string;
  [key: string]: any;
}

interface LottieAnimationData {
  v: string;
  fr: number;
  ip: number;
  op: number;
  w: number;
  h: number;
  nm: string;
  ddd: number;
  assets: LottieAsset[];
  layers: LottieLayer[];
  [key: string]: any;
}
