import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  Image, 
  Dimensions, 
  ScrollView, 
  Alert, 
  Modal, 
  FlatList, 
  KeyboardAvoidingView, 
  TouchableWithoutFeedback, 
  Keyboard,
  Platform,
  SafeAreaView 
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Link, router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import MaskedView from '@react-native-masked-view/masked-view';

const { width } = Dimensions.get('window');
const LOGO_SIZE = Math.min(width * 0.5, 150);

// Type Definitions
interface Option {
  label: string;
  value: string;
}

interface CustomDropdownProps {
  options: Option[];
  value: string;
  onSelect: (value: string) => void;
  placeholder: string;
  iconName: keyof typeof Ionicons.glyphMap;
}

interface FormState {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  birthDate: Date | null; // Changed to allow null for placeholder
  gender: string;
  country: string;
  state: string;
  city: string;
}

interface ErrorsState {
  [key: string]: string | undefined;
}

// Mock Data
const GENDERS: Option[] = [{ label: 'Male', value: 'male' }, { label: 'Female', value: 'female' }, { label: 'Other', value: 'other' }, { label: 'Prefer not to say', value: 'unspecified' }];
const COUNTRIES: Option[] = [{ label: 'Brazil', value: 'BR' }, { label: 'United States', value: 'US' }];
const STATES: { [key: string]: Option[] } = {
  'BR': [{ label: 'São Paulo', value: 'SP' }, { label: 'Rio de Janeiro', value: 'RJ' }],
  'US': [{ label: 'California', value: 'CA' }, { label: 'New York', value: 'NY' }],
};
const CITIES: { [key: string]: Option[] } = {
  'BR-SP': [{ label: 'São Paulo', value: 'São Paulo' }, { label: 'Campinas', value: 'Campinas' }],
  'BR-RJ': [{ label: 'Rio de Janeiro', value: 'Rio de Janeiro' }, { label: 'Niterói', value: 'Niterói' }],
  'US-CA': [{ label: 'Los Angeles', value: 'Los Angeles' }, { label: 'San Francisco', value: 'San Francisco' }],
  'US-NY': [{ label: 'New York City', value: 'New York City' }, { label: 'Buffalo', value: 'Buffalo' }],
};

const CustomDropdown: React.FC<CustomDropdownProps> = ({ options, value, onSelect, placeholder, iconName }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const selectedLabel = options.find(option => option.value === value)?.label || placeholder;

  return (
    <>
      <TouchableOpacity style={styles.inputContainer} onPress={() => setModalVisible(true)}>
        <Ionicons name={iconName} size={20} color="#666" style={styles.inputIcon} />
        <Text style={[styles.inputText, !value && styles.placeholderText]}>{selectedLabel}</Text>
      </TouchableOpacity>
      <Modal
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity style={styles.modalOverlay} onPress={() => setModalVisible(false)}>
          <View style={styles.modalContent}>
            <FlatList
              data={options}
              keyExtractor={(item) => item.value}
              renderItem={({ item }) => (
                <TouchableOpacity style={styles.modalItem} onPress={() => {
                  onSelect(item.value);
                  setModalVisible(false);
                }}>
                  <Text style={styles.modalItemText}>{item.label}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default function SignupScreen() {
      const [form, setForm] = useState<FormState>({ 
    firstName: '', 
    lastName: '', 
    username: '', 
    email: '', 
    birthDate: null, // Start with null to show placeholder
    gender: '', 
    country: '', 
    state: '', 
    city: '' 
  });
    
    const [errors, setErrors] = useState<ErrorsState>({});
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [availableStates, setAvailableStates] = useState<Option[]>([]);
    const [availableCities, setAvailableCities] = useState<Option[]>();

    useEffect(() => {
        if (form.country) {
            setAvailableStates(STATES[form.country] || []);
        } else {
            setAvailableStates([]);
        }
        setForm(f => ({ ...f, state: '', city: '' }));
    }, [form.country]);

    useEffect(() => {
        if (form.country && form.state) {
            setAvailableCities(CITIES[`${form.country}-${form.state}`] || []);
        } else {
            setAvailableCities([]);
        }
        setForm(f => ({ ...f, city: '' }));
    }, [form.state]);

    const validateEmail = (email: string) => {
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    };

    const validate = () => {
        const newErrors: ErrorsState = {};
        if (!form.firstName.trim()) newErrors.firstName = 'First Name is required';
        if (!form.lastName.trim()) newErrors.lastName = 'Last Name is required';
        if (!form.username.trim()) newErrors.username = 'Username is required';
        if (!form.email) {
          newErrors.email = 'Email is required';
        } else if (!validateEmail(form.email)) {
          newErrors.email = 'Please enter a valid email address';
        }
        if (!form.birthDate) newErrors.birthDate = 'Birth date is required';
        if (!form.gender) newErrors.gender = 'Gender is required';
        if (!form.country) newErrors.country = 'Country is required';
        if (availableStates.length > 0 && !form.state) newErrors.state = 'State is required';
        if (availableCities.length > 0 && !form.city) newErrors.city = 'City is required';
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const onDateChange = (event: any, selectedDate?: Date) => {
      setShowDatePicker(Platform.OS === 'ios');
      if (selectedDate && event.type !== 'dismissed') {
        const today = new Date();
        const selected = selectedDate > today ? today : selectedDate;
        setForm(prev => ({ ...prev, birthDate: selected }));
      }
    };

    const renderDatePicker = () => {
      if (Platform.OS === 'android') {
        return showDatePicker && (
          <DateTimePicker
            value={form.birthDate || new Date()}
            mode="date"
            display="default"
            onChange={onDateChange}
            maximumDate={new Date()}
            minimumDate={new Date(1900, 0, 1)}
            textColor="#FFFFFF"
            accentColor="#3EC1F9"
            themeVariant="dark"
          />
        );
      }
      
      return (
        <Modal
          visible={showDatePicker}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDatePicker(false)}
        >
          <View style={styles.datePickerOverlay}>
            <View style={styles.datePickerModal}>
              <DateTimePicker
                value={form.birthDate || new Date()}
                mode="date"
                display="spinner"
                onChange={onDateChange}
                maximumDate={new Date()}
                minimumDate={new Date(1900, 0, 1)}
                textColor="#FFFFFF"
                accentColor="#3EC1F9"
                themeVariant="dark"
                style={{ backgroundColor: '#000' }}
              />
              <TouchableOpacity 
                style={styles.datePickerButton}
                onPress={() => setShowDatePicker(false)}
                activeOpacity={0.7}
              >
                <Text style={styles.datePickerDoneText}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      );
    };

    const handleSignup = () => {
        if (validate()) {
            Alert.alert('Success', 'Account created! Please sign in.', [
                { text: 'OK', onPress: () => router.replace('/(auth)/login') },
            ]);
        }
    };

    const handleChange = (field: keyof FormState, value: string) => {
        setForm(f => ({ ...f, [field]: value }));
    };

  const renderFieldLabel = (label: string, required: boolean = true) => (
    <Text style={styles.fieldLabel}>
      {label}
      {required && <Text style={styles.requiredStar}> *</Text>}
    </Text>
  );

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 0}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContentContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <TouchableOpacity 
            style={styles.scrollableBackButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={32} color="#fff" />
          </TouchableOpacity>
          <View style={styles.content}>
            <View style={styles.form}>
              <View style={styles.logoSection}>
                <Image source={require('../../assets/images/BIG.png')} style={styles.logo} resizeMode="contain" />
                <MaskedView style={styles.titleContainer} maskElement={<Text style={styles.logoText}>HodlHub</Text>}>
                  <LinearGradient 
                    colors={['#0047AB', '#00FF9D']} 
                    start={{ x: 0, y: 0 }} 
                    end={{ x: 1, y: 0 }} 
                    style={styles.gradient} 
                  />
                </MaskedView>
              </View>
              {renderFieldLabel('First Name')}
              <View style={[
                styles.inputContainer, 
                errors.firstName && styles.inputError
              ]}>
                <Ionicons name="person-outline" size={20} color="#666" style={styles.inputIcon} />
                <TextInput 
                  style={styles.inputText} 
                  placeholder="Enter your first name" 
                  placeholderTextColor="#666" 
                  value={form.firstName} 
                  onChangeText={v => handleChange('firstName', v)}
                  returnKeyType="next"
                />
              </View>
              {errors.firstName && <Text style={styles.errorText}>{errors.firstName}</Text>}

              {renderFieldLabel('Last Name')}
              <View style={[
                styles.inputContainer, 
                errors.lastName && styles.inputError
              ]}>
                <Ionicons name="person-outline" size={20} color="#666" style={styles.inputIcon} />
                <TextInput 
                  style={styles.inputText} 
                  placeholder="Enter your last name" 
                  placeholderTextColor="#666" 
                  value={form.lastName} 
                  onChangeText={v => handleChange('lastName', v)}
                  returnKeyType="next"
                />
              </View>
              {errors.lastName && <Text style={styles.errorText}>{errors.lastName}</Text>}

              {renderFieldLabel('Username')}
              <View style={[
                styles.inputContainer, 
                errors.username && styles.inputError
              ]}>
                <Ionicons name="at-outline" size={20} color="#666" style={styles.inputIcon} />
                <TextInput 
                  style={styles.inputText} 
                  placeholder="Choose a username" 
                  placeholderTextColor="#666" 
                  value={form.username} 
                  onChangeText={v => handleChange('username', v)} 
                  autoCapitalize="none"
                  returnKeyType="next"
                />
              </View>
              {errors.username && <Text style={styles.errorText}>{errors.username}</Text>}

              {renderFieldLabel('Email')}
              <View style={[
                styles.inputContainer, 
                errors.email && styles.inputError
              ]}>
                <Ionicons name="mail-outline" size={20} color="#666" style={styles.inputIcon} />
                <TextInput 
                  style={styles.inputText} 
                  placeholder="<EMAIL>" 
                  placeholderTextColor="#666" 
                  value={form.email} 
                  onChangeText={v => handleChange('email', v)} 
                  autoCapitalize="none" 
                  keyboardType="email-address"
                  autoComplete="email"
                  textContentType="emailAddress"
                  returnKeyType="next"
                />
              </View>
              {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}

              {renderFieldLabel('Birth Date')}
              <View>
                <TouchableOpacity 
                  style={[
                    styles.inputContainer, 
                    styles.dateInputContainer,
                    errors.birthDate && styles.inputError
                  ]} 
                  onPress={() => setShowDatePicker(true)}
                >
                  <Ionicons name="calendar-outline" size={20} color="#666" style={styles.inputIcon} />
                  <Text style={form.birthDate ? styles.inputText : styles.placeholderText}>
                    {form.birthDate ? form.birthDate.toLocaleDateString('en-GB') : 'Birthday Date'}
                  </Text>
                </TouchableOpacity>
              </View>
              {renderDatePicker()}
              {errors.birthDate && <Text style={styles.errorText}>{errors.birthDate}</Text>}

              {renderFieldLabel('Gender')}
              <CustomDropdown 
                iconName="transgender-outline" 
                options={GENDERS} 
                value={form.gender} 
                onSelect={v => handleChange('gender', v)} 
                placeholder="Select Gender..." 
              />
              {errors.gender && <Text style={styles.errorText}>{errors.gender}</Text>}

              {renderFieldLabel('Country')}
              <CustomDropdown 
                iconName="earth-outline" 
                options={COUNTRIES} 
                value={form.country} 
                onSelect={v => handleChange('country', v)} 
                placeholder="Select Country..." 
              />
              {errors.country && <Text style={styles.errorText}>{errors.country}</Text>}

              {availableStates.length > 0 && (
                <View>
                  {renderFieldLabel('State')}
                  <CustomDropdown 
                    iconName="map-outline" 
                    options={availableStates} 
                    value={form.state} 
                    onSelect={v => handleChange('state', v)} 
                    placeholder="Select State..." 
                  />
                  {errors.state && <Text style={styles.errorText}>{errors.state}</Text>}
                </View>
              )}

              {availableCities && availableCities.length > 0 && (
                <View>
                  {renderFieldLabel('City')}
                  <CustomDropdown 
                    iconName="business-outline" 
                    options={availableCities} 
                    value={form.city} 
                    onSelect={v => handleChange('city', v)} 
                    placeholder="Select City..." 
                  />
                  {errors.city && <Text style={styles.errorText}>{errors.city}</Text>}
                </View>
              )}

              <View style={styles.termsContainer}>
                <Text style={styles.termsText}>
                  By creating an account you agree to our{' '}
                  <Text style={styles.termsLink} onPress={() => {}}>Terms of Service</Text> and{' '}
                  <Text style={styles.termsLink} onPress={() => {}}>Privacy Policy</Text>.
                </Text>
              </View>

              <TouchableOpacity 
                style={styles.signupButton} 
                onPress={handleSignup}
              >
                <Text style={styles.signupButtonText}>Create Account</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: { 
    flex: 1, 
    backgroundColor: '#000',
  },
  scrollableBackButton: {
    alignSelf: 'flex-start',
    marginBottom: 8,
    marginTop: Platform.OS === 'ios' ? 26 : 21,  // Increased by 8px
    marginLeft: 20,
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingTop: Platform.OS === 'ios' ? 5 : 0,  // Further reduced top padding
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  content: { 
    alignItems: 'center', 
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  logoSection: { 
    alignItems: 'center', 
    marginBottom: 8,  // Reduced from 15px to 8px
    marginTop: 0,     // No top margin
  },
  logo: { 
    width: LOGO_SIZE, 
    height: LOGO_SIZE, 
    marginBottom: -20 
  },
  titleContainer: { 
    height: 75, 
    width: width * 0.8, 
    maxWidth: 400 
  },
  logoText: { 
    fontSize: 60, 
    fontWeight: 'bold', 
    color: '#fff', 
    textAlign: 'center' 
  },
  gradient: { 
    flex: 1 
  },
  form: { 
    width: '100%', 
    maxWidth: 400,
    marginTop: 10,
  },
  fieldLabel: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 6,
    marginTop: 8,
  },
  requiredStar: {
    color: '#FF4444',
  },
  inputContainer: { 
    flexDirection: 'row', 
    alignItems: 'center', 
    backgroundColor: 'rgba(255, 255, 255, 0.1)', 
    borderRadius: 8, 
    marginBottom: 8, 
    paddingHorizontal: 16, 
    minHeight: 52,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  inputError: {
    borderColor: '#FF6B6B',
  },
  inputIcon: { 
    width: 20,
    textAlign: 'center',
  },
  inputText: { 
    flex: 1, 
    color: '#fff', 
    fontSize: 16,
    paddingVertical: 16,
    textAlignVertical: 'center',
    lineHeight: 20,
    marginLeft: 12,
  },
  placeholderText: { 
    color: '#666',
    fontSize: 16,
    flex: 1,
    marginLeft: 12,
  },
  dateInputContainer: {
    justifyContent: 'flex-start',
  },
  signupButton: { 
    backgroundColor: '#3EC1F9', 
    borderRadius: 8, 
    paddingVertical: 16,
    alignItems: 'center', 
    justifyContent: 'center', 
    marginTop: 24,
    marginBottom: 20,
  },
  signupButtonText: { 
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  termsContainer: {
    marginTop: 16,
    marginBottom: 24,
    paddingHorizontal: 8,
  },
  termsText: {
    color: '#9CA3AF',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
  termsLink: {
    color: '#3B82F6',
    textDecorationLine: 'underline',
  },
  errorText: { 
    color: '#FF6B6B', 
    fontSize: 12, 
    marginLeft: 12, 
    marginTop: -8,
    marginBottom: 4,
  },
  modalOverlay: { 
    flex: 1, 
    justifyContent: 'center', 
    alignItems: 'center', 
    backgroundColor: 'rgba(0,0,0,0.7)' 
  },
  modalContent: { 
    backgroundColor: '#1A1A1A', 
    borderRadius: 12, 
    padding: 20, 
    width: '80%', 
    maxHeight: '60%' 
  },
  modalItem: { 
    paddingVertical: 15, 
    borderBottomWidth: 1, 
    borderBottomColor: '#333' 
  },
  modalItemText: { 
    color: '#fff', 
    fontSize: 16, 
    textAlign: 'center' 
  },
  datePickerOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  datePickerModal: {
    backgroundColor: '#000',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    width: '85%',
  },
  datePickerContainer: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  datePickerContent: {
    backgroundColor: '#000',
    borderRadius: 12,
    overflow: 'hidden',
    width: '100%',
  },
  datePickerButton: {
    backgroundColor: '#3EC1F9',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 40,
    marginTop: 20,
    alignItems: 'center',
  },
  datePickerDoneText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  datePickerButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
