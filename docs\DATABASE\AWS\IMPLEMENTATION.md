# AWS Database Implementation

## Overview
This document outlines the AWS-based database architecture for the HodlHub Communities application, leveraging AWS managed services for scalability, reliability, and performance.

## Architecture Components

### 1. Primary Database: Amazon Aurora PostgreSQL
**Purpose:** Main operational database for structured data storage

**Schema Design:**
```sql
-- Users Table
CREATE TABLE users (
    user_id VARCHAR(36) PRIMARY KEY,
    name VARCHA<PERSON>(100) NOT NULL,
    handle VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    avatar_url TEXT,
    portfolio_change DECIMAL(5,2),
    is_online BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Posts Table
CREATE TABLE posts (
    post_id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) REFERENCES users(user_id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    title VARCHAR(255),
    image_url TEXT,
    performance_indicator VARCHAR(10),
    upvotes INT DEFAULT 0,
    downvotes INT DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    tsvector_doc TSVECTOR GENERATED ALWAYS AS (
        to_tsvector('english', COALESCE(title, '') || ' ' || content)
    ) STORED
);

-- Comments Table
CREATE TABLE comments (
    comment_id VARCHAR(36) PRIMARY KEY,
    post_id VARCHAR(36) REFERENCES posts(post_id) ON DELETE CASCADE,
    user_id VARCHAR(36) REFERENCES users(user_id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    likes INT DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Reactions (Polymorphic)
CREATE TABLE reactions (
    reaction_id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) REFERENCES users(user_id) ON DELETE CASCADE,
    entity_type VARCHAR(20) NOT NULL, -- 'post' or 'comment'
    entity_id VARCHAR(36) NOT NULL,   -- post_id or comment_id
    type VARCHAR(20) NOT NULL,        -- 'like', 'upvote', 'downvote', etc.
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, entity_type, entity_id, type)
);

-- User Relationships
CREATE TABLE user_relationships (
    follower_id VARCHAR(36) REFERENCES users(user_id) ON DELETE CASCADE,
    following_id VARCHAR(36) REFERENCES users(user_id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (follower_id, following_id),
    CHECK (follower_id != following_id)
);

-- Messages
CREATE TABLE conversations (
    conversation_id VARCHAR(36) PRIMARY KEY,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE conversation_participants (
    conversation_id VARCHAR(36) REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    user_id VARCHAR(36) REFERENCES users(user_id) ON DELETE CASCADE,
    last_read_at TIMESTAMPTZ,
    PRIMARY KEY (conversation_id, user_id)
);

CREATE TABLE messages (
    message_id VARCHAR(36) PRIMARY KEY,
    conversation_id VARCHAR(36) REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    sender_id VARCHAR(36) REFERENCES users(user_id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_posts_user_id ON posts(user_id);
CREATE INDEX idx_comments_post_id ON comments(post_id);
CREATE INDEX idx_reactions_entity ON reactions(entity_type, entity_id);
CREATE INDEX idx_messages_conversation ON messages(conversation_id, created_at);
CREATE INDEX idx_posts_tsvector ON posts USING GIN(tsvector_doc);
```

### 2. Amazon DynamoDB
**Purpose:** High-velocity data and real-time features

**Tables:**
1. **UserFeeds**
   - Partition Key: user_id
   - Sort Key: post_timestamp (ISO string)
   - TTL: 30 days (for ephemeral feed data)

2. **OnlineStatus**
   - Partition Key: user_id
   - TTL: 5 minutes (with DynamoDB TTL)
   - Tracks user online presence

3. **Notifications**
   - Partition Key: user_id
   - Sort Key: timestamp (ISO string)
   - TTL: 7 days

### 3. Amazon ElastiCache (Redis)
**Purpose:** Caching and real-time features

**Use Cases:**
- Session storage
- Rate limiting
- Real-time counters (like/view counts)
- Online user presence
- Feed caching

**Key Patterns:**
- `user:feed:{userId}` - Cached user feed
- `post:likes:{postId}` - Post like counter
- `user:online:{userId}` - Online status with expiry
- `rate:limit:{ip|userId}` - API rate limiting

## Data Access Layer

### 1. Data Access Objects (DAOs)
```typescript
// Example: Post DAO
class PostDAO {
  private readonly docClient: AWS.DynamoDB.DocumentClient;
  private readonly tableName: string;
  
  async createPost(post: Post): Promise<void> {
    await this.docClient.put({
      TableName: this.tableName,
      Item: post,
      ConditionExpression: 'attribute_not_exists(postId)'
    }).promise();
    
    // Update feed for followers
    await this.updateFollowerFeeds(post.userId, post);
  }
  
  async getFeed(userId: string, limit: number, lastEvaluatedKey?: any): Promise<{items: Post[], lastEvaluatedKey: any}> {
    // Check cache first
    const cacheKey = `user:feed:${userId}`;
    const cached = await redisClient.get(cacheKey);
    if (cached) return JSON.parse(cached);
    
    // Fallback to database
    const result = await this.docClient.query({
      TableName: this.tableName,
      IndexName: 'user-feed-index',
      KeyConditionExpression: 'userId = :userId',
      ExpressionAttributeValues: { ':userId': userId },
      Limit: limit,
      ExclusiveStartKey: lastEvaluatedKey,
      ScanIndexForward: false
    }).promise();
    
    // Cache the result
    await redisClient.setex(cacheKey, 60, JSON.stringify({
      items: result.Items,
      lastEvaluatedKey: result.LastEvaluatedKey
    }));
    
    return {
      items: result.Items as Post[],
      lastEvaluatedKey: result.LastEvaluatedKey
    };
  }
}
```

### 2. Real-time Subscriptions
```typescript
// Using AWS AppSync for GraphQL subscriptions
type Subscription {
  onNewPost(userId: ID!): Post @aws_subscribe(mutations: ["createPost"])
  onNewComment(postId: ID!): Comment @aws_subscribe(mutations: ["addComment"])
  onNewMessage(conversationId: ID!): Message @aws_subscribe(mutations: ["sendMessage"])
}
```

## Security & Compliance

### 1. IAM Roles & Policies
- **Database Access:**
  - Least privilege principle
  - IAM database authentication for Aurora
  - Secret rotation with AWS Secrets Manager

### 2. Encryption
- **At Rest:**
  - Aurora: AWS KMS customer-managed keys
  - DynamoDB: AWS owned keys (default) or KMS CMKs
  - ElastiCache: Encryption in transit and at rest
- **In Transit:**
  - TLS 1.2+ for all database connections
  - SSL certificate verification

### 3. Compliance
- SOC 1/2/3, ISO 27001, PCI DSS, HIPAA
- GDPR: Data residency controls, DPA with AWS

## Performance Optimization

### 1. Read Replicas
- Aurora Read Replicas for read scaling
- Cross-region replication for global users

### 2. Caching Strategy
- Multi-layer caching:
  - Application-level: In-memory cache
  - Distributed: ElastiCache (Redis)
  - Database: Aurora buffer cache

### 3. Query Optimization
- Materialized views for common queries
- Query plan analysis with Performance Insights
- Connection pooling with RDS Proxy

## Monitoring & Operations

### 1. CloudWatch Alarms
- High CPU/Memory usage
- Replication lag
- Connection count
- Cache hit/miss ratio

### 2. Backup & Recovery
- Automated backups with point-in-time recovery
- Cross-region backups for disaster recovery
- Regular restore testing

## Migration Strategy

### 1. Phase 1: Data Migration
1. Schema creation in Aurora
2. Data migration using AWS DMS or custom scripts
3. Data validation and reconciliation

### 2. Phase 2: Application Updates
1. Update data access layer
2. Implement caching layer
3. Add real-time subscriptions

### 3. Phase 3: Cutover
1. Dual-write during transition
2. Traffic shifting with Route 53
3. Rollback plan

## Cost Estimation

### 1. Aurora PostgreSQL
- db.t3.medium: $60/month
- Storage: $0.10/GB/month
- I/O: $0.20/million requests

### 2. DynamoDB
- $1.25 per million write request units
- $0.25 per million read request units
- Storage: $0.25/GB/month

### 3. ElastiCache (Redis)
- cache.t3.micro: $15/month
- Data transfer: $0.09/GB (out to internet)

## Next Steps

1. Set up VPC and networking
2. Configure IAM roles and security groups
3. Deploy Aurora cluster
4. Set up monitoring and alerting
5. Implement data migration
6. Update application code
7. Test performance and scale
8. Plan cutover

---
*Document Version: 1.0*
*Last Updated: 2024-06-14*
