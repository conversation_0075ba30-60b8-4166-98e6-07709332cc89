import React from 'react';
import { StyleSheet, View, Image } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withRepeat, withTiming, Easing } from 'react-native-reanimated';

const PaginationLoading = () => {
  const rotation = useSharedValue(0);

  rotation.value = withRepeat(
    withTiming(360, { duration: 1500, easing: Easing.linear }),
    -1, // Infinite repetitions
    false
  );

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotation.value}deg` }],
    };
  });

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.whaleContainer, animatedStyle]}>
        <Image 
          source={require('../assets/images/BIG.png')} 
          style={styles.whaleImage}
          resizeMode="contain"
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingVertical: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  whaleContainer: {
    width: 36,
    height: 36,
  },
  whaleImage: {
    width: '100%',
    height: '100%',
  },
});

export default PaginationLoading;
