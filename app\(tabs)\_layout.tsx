import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons'; // Using only Ionicons for consistency

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarShowLabel: false, // Ensures icon-only display
        tabBarStyle: {
          backgroundColor: '#000000', // Dark background
          borderTopColor: '#222222', // Dark border
          paddingTop: 10, // Improved padding for better icon presentation
          height: 70, // Increased height for better visual balance
        },
        tabBarActiveTintColor: '#3EC1F9', // Active icon color (e.g., a bright blue)
        tabBarInactiveTintColor: '#888888', // Inactive icon color (e.g., gray)
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'index') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'wallet') {
            iconName = focused ? 'wallet' : 'wallet-outline';
          } else if (route.name === 'community') {
            iconName = focused ? 'people' : 'people-outline';
          } else if (route.name === 'rankings') {
            iconName = focused ? 'stats-chart' : 'stats-chart-outline';
          } else if (route.name === 'profile') {
            iconName = focused ? 'person-circle' : 'person-circle-outline';
          }

          // Fallback icon if needed, though all routes should be covered
          if (!iconName) {
            iconName = 'ellipse-outline';
          }

          return <Ionicons name={iconName as any} size={size} color={color} />;
        },
      })}
    >
      <Tabs.Screen
        name="index"
        options={{
          // tabBarIcon is handled by screenOptions
        }}
      />
      <Tabs.Screen
        name="wallet"
        options={{
          // tabBarIcon is handled by screenOptions
        }}
      />
      <Tabs.Screen
        name="community"
        options={{
          // tabBarIcon is handled by screenOptions
        }}
      />
      <Tabs.Screen
        name="rankings"
        options={{
          // tabBarIcon is handled by screenOptions
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          // tabBarIcon is handled by screenOptions
        }}
      />
    </Tabs>
  );
}