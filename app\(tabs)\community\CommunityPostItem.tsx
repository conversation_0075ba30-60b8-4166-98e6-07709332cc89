import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface Reaction {
  emoji: string;
  count: number;
  isActive: boolean;
}

interface Comment {
  id: string;
  author: string;
  avatar: string;
  content: string;
  timestamp: string;
  likes: number;
  isLiked: boolean;
  isModerator?: boolean;
}

export interface CommunityPostItemProps {
  id: string;
  author: string;
  handle: string;
  avatar: string;
  performance?: string;
  performanceType?: 'positive' | 'negative';
  date: string;
  title: string;
  content: string;
  reactions: Reaction[];
  upvotes: number;
  downvotes: number;
  comments: Comment[];
  isUpvoted: boolean;
  isDownvoted: boolean;
  isPinned?: boolean;
  memberOnly?: boolean;
  moderatorPost?: boolean;
  onPressComment: () => void;
  onPressReaction: (reactionIndex: number) => void;
  onPressUpvote: () => void;
  onPressDownvote: () => void;
  onPressShare: () => void;
  onPressOptions: () => void;
}

export const CommunityPostItem: React.FC<CommunityPostItemProps> = ({
  author,
  handle,
  avatar,
  performance,
  performanceType,
  date,
  title,
  content,
  reactions,
  upvotes,
  downvotes,
  isUpvoted,
  isDownvoted,
  isPinned,
  memberOnly,
  moderatorPost,
  onPressComment,
  onPressReaction,
  onPressUpvote,
  onPressDownvote,
  onPressShare,
  onPressOptions,
}) => {
  const [showAllReactions, setShowAllReactions] = useState(false);

  const renderPerformanceBadge = () => {
    if (!performance) return null;
    
    return (
      <View style={[
        styles.performanceBadge,
        performanceType === 'positive' ? styles.performancePositive : styles.performanceNegative
      ]}>
        <Text style={styles.performanceText}>
          {performanceType === 'positive' ? '↑' : '↓'} {performance}
        </Text>
      </View>
    );
  };

  const renderReactions = () => {
    return (
      <View style={styles.reactionsContainer}>
        {reactions.slice(0, showAllReactions ? reactions.length : 3).map((reaction, index) => (
          <TouchableOpacity 
            key={index} 
            style={[
              styles.reactionButton,
              reaction.isActive && styles.activeReaction
            ]}
            onPress={() => onPressReaction(index)}
          >
            <Text style={styles.reactionEmoji}>{reaction.emoji}</Text>
            <Text style={styles.reactionCount}>{reaction.count}</Text>
          </TouchableOpacity>
        ))}
        {reactions.length > 3 && !showAllReactions && (
          <TouchableOpacity 
            style={styles.moreReactions}
            onPress={() => setShowAllReactions(true)}
          >
            <Text style={styles.moreReactionsText}>+{reactions.length - 3} more</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.userInfo}>
          <Image source={{ uri: avatar }} style={styles.avatar} />
          <View>
            <View style={styles.nameContainer}>
              <Text style={styles.author} numberOfLines={1}>
                {author}
                {moderatorPost && (
                  <Text style={styles.moderatorBadge}> MOD</Text>
                )}
              </Text>
              {isPinned && (
                <Ionicons name="pin" size={14} color="#888" style={styles.pinIcon} />
              )}
              {memberOnly && (
                <View style={styles.memberOnlyBadge}>
                  <Ionicons name="lock-closed" size={10} color="#fff" />
                </View>
              )}
            </View>
            <View style={styles.metaContainer}>
              <Text style={styles.handle}>@{handle}</Text>
              <Text style={styles.dot}>•</Text>
              <Text style={styles.date}>{date}</Text>
              {renderPerformanceBadge()}
            </View>
          </View>
        </View>
        <TouchableOpacity onPress={onPressOptions}>
          <Ionicons name="ellipsis-horizontal" size={20} color="#888" />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {title ? <Text style={styles.title}>{title}</Text> : null}
        <Text style={styles.postText}>{content}</Text>
      </View>

      {renderReactions()}

      <View style={styles.footer}>
        <TouchableOpacity 
          style={[styles.actionButton, isUpvoted && styles.activeActionButton]} 
          onPress={onPressUpvote}
        >
          <Ionicons 
            name="arrow-up" 
            size={18} 
            color={isUpvoted ? '#4CAF50' : '#888'} 
          />
          <Text style={[styles.actionText, isUpvoted && styles.activeActionText]}>
            {upvotes || ''}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.actionButton, isDownvoted && styles.activeActionButton]} 
          onPress={onPressDownvote}
        >
          <Ionicons 
            name="arrow-down" 
            size={18} 
            color={isDownvoted ? '#F44336' : '#888'} 
          />
          <Text style={[styles.actionText, isDownvoted && styles.activeActionText]}>
            {downvotes || ''}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={onPressComment}>
          <Ionicons name="chatbubble-ellipses-outline" size={18} color="#888" />
          <Text style={styles.actionText}>Comment</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={onPressShare}>
          <Ionicons name="share-social-outline" size={18} color="#888" />
          <Text style={styles.actionText}>Share</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#1E1E1E',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#2A2A2A',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  author: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 15,
    marginRight: 6,
  },
  metaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  handle: {
    color: '#888',
    fontSize: 13,
    marginRight: 4,
  },
  dot: {
    color: '#888',
    fontSize: 13,
    marginHorizontal: 4,
  },
  date: {
    color: '#888',
    fontSize: 13,
  },
  performanceBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 6,
  },
  performancePositive: {
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
  },
  performanceNegative: {
    backgroundColor: 'rgba(244, 67, 54, 0.2)',
  },
  performanceText: {
    fontSize: 11,
    fontWeight: '600',
  },
  moderatorBadge: {
    color: '#4CAF50',
    fontSize: 11,
    fontWeight: '600',
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
    paddingHorizontal: 4,
    borderRadius: 4,
    marginLeft: 4,
  },
  memberOnlyBadge: {
    backgroundColor: '#4CAF50',
    borderRadius: 4,
    padding: 2,
    marginLeft: 4,
  },
  pinIcon: {
    marginLeft: 4,
  },
  content: {
    marginBottom: 12,
  },
  title: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  postText: {
    color: '#E0E0E0',
    fontSize: 15,
    lineHeight: 20,
  },
  reactionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
    gap: 8,
  },
  reactionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 8,
    marginRight: 8,
    marginBottom: 4,
  },
  activeReaction: {
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  reactionEmoji: {
    fontSize: 14,
    marginRight: 4,
  },
  reactionCount: {
    color: '#E0E0E0',
    fontSize: 12,
    fontWeight: '500',
  },
  moreReactions: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#2A2A2A',
  },
  moreReactionsText: {
    color: '#888',
    fontSize: 10,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#2A2A2A',
    paddingTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  activeActionButton: {
    // Add any active state styles if needed
  },
  actionText: {
    color: '#888',
    fontSize: 13,
    marginLeft: 4,
  },
  activeActionText: {
    color: '#4CAF50',
    fontWeight: '600',
  },
});

export default CommunityPostItem;
