warning: in the working copy of 'package-lock.json', <PERSON><PERSON> will be replaced by <PERSON><PERSON> the next time Git touches it
[1mdiff --git a/package-lock.json b/package-lock.json[m
[1mindex b96ad81..ea0e4f8 100644[m
[1m--- a/package-lock.json[m
[1m+++ b/package-lock.json[m
[36m@@ -27,8 +27,9 @@[m
         "react-native-reanimated": "~3.17.4",[m
         "react-native-safe-area-context": "5.4.0",[m
         "react-native-screens": "~4.11.1",[m
[31m-        "react-native-svg": "^15.0.0",[m
[32m+[m[32m        "react-native-svg": "^15.11.2",[m
         "react-native-web": "^0.20.0",[m
[32m+[m[32m        "recharts": "^2.15.3",[m
         "victory-native": "^36.6.11"[m
       },[m
       "devDependencies": {[m
[36m@@ -5939,6 +5940,15 @@[m
         "url": "https://github.com/sponsors/sindresorhus"[m
       }[m
     },[m
[32m+[m[32m    "node_modules/clsx": {[m
[32m+[m[32m      "version": "2.1.1",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz",[m
[32m+[m[32m      "integrity": "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==",[m
[32m+[m[32m      "license": "MIT",[m
[32m+[m[32m      "engines": {[m
[32m+[m[32m        "node": ">=6"[m
[32m+[m[32m      }[m
[32m+[m[32m    },[m
     "node_modules/co": {[m
       "version": "4.6.0",[m
       "resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz",[m
[36m@@ -6710,8 +6720,7 @@[m
     "node_modules/csstype": {[m
       "version": "3.1.3",[m
       "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz",[m
[31m-      "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==",[m
[31m-      "dev": true[m
[32m+[m[32m      "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="[m
     },[m
     "node_modules/d3-array": {[m
       "version": "3.2.4",[m
[36m@@ -6939,6 +6948,12 @@[m
       "integrity": "sha512-8vDa8Qxvr/+d94hSh5P3IJwI5t8/c0KsMp+g8bNw9cY2icONa5aPfvKeieW1WlG0WQYwwhJ7mjui2xtiePQSXw==",[m
       "dev": true[m
     },[m
[32m+[m[32m    "node_modules/decimal.js-light": {[m
[32m+[m[32m      "version": "2.5.1",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/decimal.js-light/-/decimal.js-light-2.5.1.tgz",[m
[32m+[m[32m      "integrity": "sha512-qIMFpTMZmny+MMIitAB6D7iVPEorVw6YQRWkvarTkT4tBeSLLiHzcwj6q0MmYSFCiVpiqPJTJEYIrpcPzVEIvg==",[m
[32m+[m[32m      "license": "MIT"[m
[32m+[m[32m    },[m
     "node_modules/decode-uri-component": {[m
       "version": "0.2.2",[m
       "resolved": "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.2.tgz",[m
[36m@@ -7211,6 +7226,16 @@[m
         "utila": "~0.4"[m
       }[m
     },[m
[32m+[m[32m    "node_modules/dom-helpers": {[m
[32m+[m[32m      "version": "5.2.1",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz",[m
[32m+[m[32m      "integrity": "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==",[m
[32m+[m[32m      "license": "MIT",[m
[32m+[m[32m      "dependencies": {[m
[32m+[m[32m        "@babel/runtime": "^7.8.7",[m
[32m+[m[32m        "csstype": "^3.0.2"[m
[32m+[m[32m      }[m
[32m+[m[32m    },[m
     "node_modules/dom-serializer": {[m
       "version": "1.4.1",[m
       "resolved": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.4.1.tgz",[m
[36m@@ -7729,8 +7754,7 @@[m
     "node_modules/eventemitter3": {[m
       "version": "4.0.7",[m
       "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz",[m
[31m-      "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==",[m
[31m-      "dev": true[m
[32m+[m[32m      "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw=="[m
     },[m
     "node_modules/events": {[m
       "version": "3.3.0",[m
[36m@@ -9431,6 +9455,15 @@[m
       "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz",[m
       "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="[m
     },[m
[32m+[m[32m    "node_modules/fast-equals": {[m
[32m+[m[32m      "version": "5.2.2",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.2.2.tgz",[m
[32m+[m[32m      "integrity": "sha512-V7/RktU11J3I36Nwq2JnZEM7tNm17eBJz+u25qdxBZeCKiX6BkVSZQjwWIr+IobgnZy+ag73tTZgZi7tr0LrBw==",[m
[32m+[m[32m      "license": "MIT",[m
[32m+[m[32m      "engines": {[m
[32m+[m[32m        "node": ">=6.0.0"[m
[32m+[m[32m      }[m
[32m+[m[32m    },[m
     "node_modules/fast-glob": {[m
       "version": "3.3.3",[m
       "resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz",[m
[36m@@ -16015,6 +16048,23 @@[m
         "node": ">= 6"[m
       }[m
     },[m
[32m+[m[32m    "node_modules/prop-types": {[m
[32m+[m[32m      "version": "15.8.1",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz",[m
[32m+[m[32m      "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==",[m
[32m+[m[32m      "license": "MIT",[m
[32m+[m[32m      "dependencies": {[m
[32m+[m[32m        "loose-envify": "^1.4.0",[m
[32m+[m[32m        "object-assign": "^4.1.1",[m
[32m+[m[32m        "react-is": "^16.13.1"[m
[32m+[m[32m      }[m
[32m+[m[32m    },[m
[32m+[m[32m    "node_modules/prop-types/node_modules/react-is": {[m
[32m+[m[32m      "version": "16.13.1",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz",[m
[32m+[m[32m      "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==",[m
[32m+[m[32m      "license": "MIT"[m
[32m+[m[32m    },[m
     "node_modules/proxy-addr": {[m
       "version": "2.0.7",[m
       "resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz",[m
[36m@@ -16442,9 +16492,10 @@[m
       }[m
     },[m
     "node_modules/react-native-svg": {[m
[31m-      "version": "15.12.0",[m
[31m-      "resolved": "https://registry.npmjs.org/react-native-svg/-/react-native-svg-15.12.0.tgz",[m
[31m-      "integrity": "sha512-iE25PxIJ6V0C6krReLquVw6R0QTsRTmEQc4K2Co3P6zsimU/jltcDBKYDy1h/5j9S/fqmMeXnpM+9LEWKJKI6A==",[m
[32m+[m[32m      "version": "15.11.2",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/react-native-svg/-/react-native-svg-15.11.2.tgz",[m
[32m+[m[32m      "integrity": "sha512-+YfF72IbWQUKzCIydlijV1fLuBsQNGMT6Da2kFlo1sh+LE3BIm/2Q7AR1zAAR6L0BFLi1WaQPLfFUC9bNZpOmw==",[m
[32m+[m[32m      "license": "MIT",[m
       "dependencies": {[m
         "css-select": "^5.1.0",[m
         "css-tree": "^1.1.3",[m
[36m@@ -16630,6 +16681,37 @@[m
         "node": ">=0.10.0"[m
       }[m
     },[m
[32m+[m[32m    "node_modules/react-smooth": {[m
[32m+[m[32m      "version": "4.0.4",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/react-smooth/-/react-smooth-4.0.4.tgz",[m
[32m+[m[32m      "integrity": "sha512-gnGKTpYwqL0Iii09gHobNolvX4Kiq4PKx6eWBCYYix+8cdw+cGo3do906l1NBPKkSWx1DghC1dlWG9L2uGd61Q==",[m
[32m+[m[32m      "license": "MIT",[m
[32m+[m[32m      "dependencies": {[m
[32m+[m[32m        "fast-equals": "^5.0.1",[m
[32m+[m[32m        "prop-types": "^15.8.1",[m
[32m+[m[32m        "react-transition-group": "^4.4.5"[m
[32m+[m[32m      },[m
[32m+[m[32m      "peerDependencies": {[m
[32m+[m[32m        "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0",[m
[32m+[m[32m        "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"[m
[32m+[m[32m      }[m
[32m+[m[32m    },[m
[32m+[m[32m    "node_modules/react-transition-group": {[m
[32m+[m[32m      "version": "4.4.5",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz",[m
[32m+[m[32m      "integrity": "sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==",[m
[32m+[m[32m      "license": "BSD-3-Clause",[m
[32m+[m[32m      "dependencies": {[m
[32m+[m[32m        "@babel/runtime": "^7.5.5",[m
[32m+[m[32m        "dom-helpers": "^5.0.1",[m
[32m+[m[32m        "loose-envify": "^1.4.0",[m
[32m+[m[32m        "prop-types": "^15.6.2"[m
[32m+[m[32m      },[m
[32m+[m[32m      "peerDependencies": {[m
[32m+[m[32m        "react": ">=16.6.0",[m
[32m+[m[32m        "react-dom": ">=16.6.0"[m
[32m+[m[32m      }[m
[32m+[m[32m    },[m
     "node_modules/read-chunk": {[m
       "version": "3.2.0",[m
       "resolved": "https://registry.npmjs.org/read-chunk/-/read-chunk-3.2.0.tgz",[m
[36m@@ -16697,6 +16779,44 @@[m
         "url": "https://github.com/sponsors/jonschlinkert"[m
       }[m
     },[m
[32m+[m[32m    "node_modules/recharts": {[m
[32m+[m[32m      "version": "2.15.3",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/recharts/-/recharts-2.15.3.tgz",[m
[32m+[m[32m      "integrity": "sha512-EdOPzTwcFSuqtvkDoaM5ws/Km1+WTAO2eizL7rqiG0V2UVhTnz0m7J2i0CjVPUCdEkZImaWvXLbZDS2H5t6GFQ==",[m
[32m+[m[32m      "license": "MIT",[m
[32m+[m[32m      "dependencies": {[m
[32m+[m[32m        "clsx": "^2.0.0",[m
[32m+[m[32m        "eventemitter3": "^4.0.1",[m
[32m+[m[32m        "lodash": "^4.17.21",[m
[32m+[m[32m        "react-is": "^18.3.1",[m
[32m+[m[32m        "react-smooth": "^4.0.4",[m
[32m+[m[32m        "recharts-scale": "^0.4.4",[m
[32m+[m[32m        "tiny-invariant": "^1.3.1",[m
[32m+[m[32m        "victory-vendor": "^36.6.8"[m
[32m+[m[32m      },[m
[32m+[m[32m      "engines": {[m
[32m+[m[32m        "node": ">=14"[m
[32m+[m[32m      },[m
[32m+[m[32m      "peerDependencies": {[m
[32m+[m[32m        "react": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0",[m
[32m+[m[32m        "react-dom": "^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"[m
[32m+[m[32m      }[m
[32m+[m[32m    },[m
[32m+[m[32m    "node_modules/recharts-scale": {[m
[32m+[m[32m      "version": "0.4.5",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.4.5.tgz",[m
[32m+[m[32m      "integrity": "sha512-kivNFO+0OcUNu7jQquLXAxz1FIwZj8nrj+YkOKc5694NbjCvcT6aSZiIzNzd2Kul4o4rTto8QVR9lMNtxD4G1w==",[m
[32m+[m[32m      "license": "MIT",[m
[32m+[m[32m      "dependencies": {[m
[32m+[m[32m        "decimal.js-light": "^2.4.1"[m
[32m+[m[32m      }[m
[32m+[m[32m    },[m
[32m+[m[32m    "node_modules/recharts/node_modules/react-is": {[m
[32m+[m[32m      "version": "18.3.1",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz",[m
[32m+[m[32m      "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==",[m
[32m+[m[32m      "license": "MIT"[m
[32m+[m[32m    },[m
     "node_modules/reflect.getprototypeof": {[m
       "version": "1.0.10",[m
       "resolved": "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz",[m
[36m@@ -18687,6 +18807,12 @@[m
       "integrity": "sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==",[m
       "dev": true[m
     },[m
[32m+[m[32m    "node_modules/tiny-invariant": {[m
[32m+[m[32m      "version": "1.3.3",[m
[32m+[m[32m      "resolved": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.3.tgz",[m
[32m+[m[32m      "integrity": "sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==",[m
[32m+[m[32m      "license": "MIT"[m
[32m+[m[32m    },[m
     "node_modules/tmpl": {[m
       "version": "1.0.5",[m
       "resolved": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz",[m
