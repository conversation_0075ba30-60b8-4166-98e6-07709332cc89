export interface Community {
  id: string;
  name: string;
  description: string;
  members: number;
  image: string;
  joined: boolean;
  isPremium?: boolean;
  isPrivate?: boolean;
  price?: number;
  category?: string;
  lastActive?: string;
}

export interface CommunitiesState {
  data: Community[];
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  error: string | null;
  hasMore: boolean;
}

export interface UseCommunitiesReturnType extends CommunitiesState {
  searchQuery: string;
  filteredCommunities: Community[];
  handleSearch: (text: string) => void;
  handleRefresh: () => Promise<void>;
  handleLoadMore: () => Promise<void>;
  handleJoinToggle: (communityId: string, isJoined: boolean) => void;
  handleCreateCommunity: () => void;
  handleClearSearch: () => void;
}

export interface CommunityItemProps {
  item: Community;
  onJoinToggle: (id: string, isJoining: boolean) => void;
}

export interface LoadingStateProps {
  size?: number;
  fullScreen?: boolean;
  message?: string;
}

export interface UseCommunitiesReturn {
  communities: Community[];
  filteredCommunities: Community[];
  searchQuery: string;
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  handleSearch: (text: string) => void;
  handleRefresh: () => Promise<void>;
  loadMoreCommunities: () => Promise<void>;
  handleJoinToggle: (id: string, isJoining: boolean) => void;
}

export const BASE_COMMUNITIES: Community[] = [
  // Finance & Trading
  {
    id: 'bitcoin-bulls',
    name: 'Bitcoin Bulls',
    description: 'Community for long-term Bitcoin investors and HODLers',
    members: 2547,
    image: 'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
    joined: true,
    category: 'Finance',
    lastActive: '2 hours ago',
  },
  {
    id: 'defi-pioneers',
    name: 'DeFi Pioneers',
    description: 'Exploring the future of decentralized finance',
    members: 1832,
    image: 'https://assets.coingecko.com/coins/images/12504/large/uni.jpg',
    joined: true,
    category: 'Finance',
    lastActive: '1 hour ago',
  },
  {
    id: 'trading-masters',
    name: 'Trading Masters',
    description: 'Advanced trading strategies and market analysis',
    members: 4521,
    image: 'https://assets.coingecko.com/coins/images/825/large/bnb-icon2_2x.png',
    joined: false,
    category: 'Finance',
    lastActive: '30 minutes ago',
    isPremium: true,
    price: 29.99,
  },
  {
    id: 'altcoin-gems',
    name: 'Altcoin Gems',
    description: 'Discovering the next big altcoin opportunities',
    members: 3156,
    image: 'https://assets.coingecko.com/coins/images/4713/large/matic-token-icon.png',
    joined: false,
    category: 'Finance',
    lastActive: '45 minutes ago',
  },

  // Technology
  {
    id: 'ai-ml-innovators',
    name: 'AI/ML Innovators',
    description: 'Artificial Intelligence and Machine Learning discussions',
    members: 2891,
    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400',
    joined: true,
    category: 'Technology',
    lastActive: '1 hour ago',
  },
  {
    id: 'web3-developers',
    name: 'Web3 Developers',
    description: 'Building the decentralized web together',
    members: 1967,
    image: 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=400',
    joined: false,
    category: 'Technology',
    lastActive: '3 hours ago',
  },
  {
    id: 'cybersecurity-pros',
    name: 'Cybersecurity Pros',
    description: 'Protecting digital assets and privacy',
    members: 1543,
    image: 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=400',
    joined: false,
    category: 'Technology',
    lastActive: '2 hours ago',
    isPrivate: true,
  },

  // Creative & NFTs
  {
    id: 'nft-collectors',
    name: 'NFT Collectors',
    description: 'Digital art and NFT trading community',
    members: 3211,
    image: 'https://assets.coingecko.com/coins/images/13446/large/5f6294c0c7a8cda55cb1c936_Flow_Wordmark.png',
    joined: false,
    category: 'Creative',
    lastActive: '4 hours ago',
  },
  {
    id: 'digital-artists',
    name: 'Digital Artists',
    description: 'Showcase and discuss digital art creations',
    members: 2734,
    image: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400',
    joined: true,
    category: 'Creative',
    lastActive: '1 hour ago',
  },
  {
    id: 'content-creators',
    name: 'Content Creators',
    description: 'Tips and strategies for content creation',
    members: 4102,
    image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400',
    joined: false,
    category: 'Creative',
    lastActive: '2 hours ago',
  },

  // Lifestyle
  {
    id: 'fitness-crypto',
    name: 'Fitness & Crypto',
    description: 'Combining health goals with crypto rewards',
    members: 1876,
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
    joined: true,
    category: 'Lifestyle',
    lastActive: '5 hours ago',
  },
  {
    id: 'travel-nomads',
    name: 'Digital Nomads',
    description: 'Travel the world while earning crypto',
    members: 2345,
    image: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400',
    joined: false,
    category: 'Lifestyle',
    lastActive: '3 hours ago',
  },
  {
    id: 'crypto-education',
    name: 'Crypto Education',
    description: 'Learn about blockchain and cryptocurrency',
    members: 5432,
    image: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400',
    joined: true,
    category: 'Education',
    lastActive: '1 hour ago',
  },

  // Gaming
  {
    id: 'gamefi-players',
    name: 'GameFi Players',
    description: 'Play-to-earn gaming and NFT games',
    members: 3789,
    image: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400',
    joined: false,
    category: 'Gaming',
    lastActive: '2 hours ago',
  },
  {
    id: 'metaverse-builders',
    name: 'Metaverse Builders',
    description: 'Building virtual worlds and experiences',
    members: 2156,
    image: 'https://images.unsplash.com/photo-1592478411213-6153e4ebc696?w=400',
    joined: false,
    category: 'Technology',
    lastActive: '6 hours ago',
    isPremium: true,
    price: 19.99,
  },
];
