import { useState, useCallback } from 'react';

export const usePagination = (loadMore: () => Promise<void>) => {
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const handleLoadMore = useCallback(async () => {
    if (isLoadingMore) return;
    
    try {
      setIsLoadingMore(true);
      await loadMore();
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, loadMore]);

  return { isLoadingMore, handleLoadMore };
};
