import React, { useState, useRef, useEffect } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  Animated, 
  KeyboardAvoidingView, 
  Platform,
  Keyboard,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface CommunityPostComposerProps {
  visible: boolean;
  onPost: (content: string) => void;
  onCancel: () => void;
  placeholder?: string;
  initialValue?: string;
  buttonText?: string;
  avatar?: string;
}

export const CommunityPostComposer: React.FC<CommunityPostComposerProps> = ({
  visible,
  onPost,
  onCancel,
  placeholder = "What's on your mind?",
  initialValue = '',
  buttonText = 'Post',
  avatar = 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
}) => {
  const [content, setContent] = useState(initialValue);
  const [keyboardHeight] = useState(new Animated.Value(0));
  const inputRef = useRef<TextInput>(null);

  useEffect(() => {
    const keyboardWillShow = (e: any) => {
      Animated.timing(keyboardHeight, {
        duration: e.duration,
        toValue: e.endCoordinates.height,
        useNativeDriver: false,
      }).start();
    };

    const keyboardWillHide = () => {
      Animated.timing(keyboardHeight, {
        duration: 250,
        toValue: 0,
        useNativeDriver: false,
      }).start();
    };

    const showSubscription = Keyboard.addListener('keyboardWillShow', keyboardWillShow);
    const hideSubscription = Keyboard.addListener('keyboardWillHide', keyboardWillHide);

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, [keyboardHeight]);

  useEffect(() => {
    if (visible) {
      // Small delay to ensure the component is rendered before focusing
      const timer = setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [visible]);

  const handlePost = () => {
    if (content.trim()) {
      onPost(content);
      setContent('');
      Keyboard.dismiss();
    }
  };

  const handleCancel = () => {
    setContent('');
    Keyboard.dismiss();
    onCancel();
  };

  if (!visible) return null;

  return (
    <KeyboardAvoidingView 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <View style={styles.overlay} />
      
      <View style={styles.composerContainer}>
        <View style={styles.composerHeader}>
          <Text style={styles.composerTitle}>Create Post</Text>
          <TouchableOpacity onPress={handleCancel}>
            <Ionicons name="close" size={24} color="#8E8E93" />
          </TouchableOpacity>
        </View>

        <View style={styles.composerContent}>
          <Image source={{ uri: avatar }} style={styles.avatar} />
          <TextInput
            ref={inputRef}
            style={styles.input}
            placeholder={placeholder}
            placeholderTextColor="#8E8E93"
            value={content}
            onChangeText={setContent}
            multiline
            autoFocus
          />
        </View>

        <View style={styles.composerActions}>
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="image-outline" size={24} color="#007AFF" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="link-outline" size={24} color="#007AFF" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="happy-outline" size={24} color="#FFC107" />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[
              styles.postButton,
              !content.trim() && styles.postButtonDisabled
            ]}
            onPress={handlePost}
            disabled={!content.trim()}
          >
            <Text style={styles.postButtonText}>{buttonText}</Text>
          </TouchableOpacity>
        </View>
      </View>

      <Animated.View style={[styles.keyboardSpacer, { height: keyboardHeight }]} />
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  composerContainer: {
    backgroundColor: '#1C1C1E',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    padding: 16,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 1001,
  },
  composerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  composerTitle: {
    color: '#FFF',
    fontSize: 17,
    fontWeight: '600',
  },
  composerContent: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  input: {
    flex: 1,
    color: '#FFF',
    fontSize: 16,
    lineHeight: 22,
    maxHeight: 200,
    textAlignVertical: 'top',
  },
  composerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#2C2C2E',
  },
  actionButtons: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginRight: 8,
  },
  postButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 17,
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  postButtonDisabled: {
    opacity: 0.5,
  },
  postButtonText: {
    color: '#FFF',
    fontWeight: '600',
    fontSize: 15,
  },
  keyboardSpacer: {
    backgroundColor: '#1C1C1E',
  },
});

export default CommunityPostComposer;
